package com.talkweb.ai.indexer.logging;

import com.talkweb.ai.indexer.model.OcrResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OCR日志记录器测试类
 */
class OcrLoggerTest {

    private OcrLogger ocrLogger;

    @BeforeEach
    void setUp() {
        ocrLogger = new OcrLogger();
    }

    @Test
    void testStartProcessingWithFile() throws IOException {
        // Given
        File tempFile = File.createTempFile("test", ".png");
        tempFile.deleteOnExit();

        // When
        String requestId = ocrLogger.startProcessing(tempFile);

        // Then
        assertNotNull(requestId);
        assertTrue(requestId.startsWith("ocr_"));
        assertEquals(1, ocrLogger.getActiveContextCount());
        
        OcrLogger.ProcessingContext context = ocrLogger.getContext(requestId);
        assertNotNull(context);
        assertEquals(tempFile.getName(), context.getImageName());
        assertEquals("file", context.getImageType());
    }

    @Test
    void testStartProcessingWithBufferedImage() {
        // Given
        BufferedImage image = createTestImage(100, 100, "Test");

        // When
        String requestId = ocrLogger.startProcessing(image, "test_image.png");

        // Then
        assertNotNull(requestId);
        assertTrue(requestId.startsWith("ocr_"));
        assertEquals(1, ocrLogger.getActiveContextCount());
        
        OcrLogger.ProcessingContext context = ocrLogger.getContext(requestId);
        assertNotNull(context);
        assertEquals("test_image.png", context.getImageName());
        assertEquals("buffered", context.getImageType());
    }

    @Test
    void testLogProcessingStage() {
        // Given
        BufferedImage image = createTestImage(100, 100, "Test");
        String requestId = ocrLogger.startProcessing(image, "test.png");

        // When
        ocrLogger.logProcessingStage(requestId, "preprocessing", "Image preprocessing started");
        ocrLogger.logProcessingStage(requestId, "ocr_recognition", "OCR recognition in progress");

        // Then
        OcrLogger.ProcessingContext context = ocrLogger.getContext(requestId);
        assertNotNull(context);
        assertEquals(2, context.getStageCount());
        assertTrue(context.getStages().containsKey("preprocessing"));
        assertTrue(context.getStages().containsKey("ocr_recognition"));
    }

    @Test
    void testLogPerformance() {
        // Given
        BufferedImage image = createTestImage(100, 100, "Test");
        String requestId = ocrLogger.startProcessing(image, "test.png");
        Map<String, Object> metrics = Map.of(
            "confidence", 85.5,
            "text_length", 100,
            "word_count", 15
        );

        // When
        ocrLogger.logPerformance(requestId, "ocr_complete", 1500, metrics);

        // Then - Should not throw any exceptions
        assertDoesNotThrow(() -> ocrLogger.logPerformance(requestId, "test_operation", 100, metrics));
    }

    @Test
    void testLogError() {
        // Given
        BufferedImage image = createTestImage(100, 100, "Test");
        String requestId = ocrLogger.startProcessing(image, "test.png");
        Exception testException = new RuntimeException("Test error");
        Map<String, Object> errorContext = Map.of(
            "image_size", "100x100",
            "processing_stage", "ocr_recognition"
        );

        // When
        ocrLogger.logError(requestId, "test_error", "Test error message", testException, errorContext);

        // Then
        OcrLogger.ProcessingContext context = ocrLogger.getContext(requestId);
        assertNotNull(context);
        assertTrue(context.hasErrors());
        assertTrue(context.getErrors().containsKey("test_error"));
        
        OcrLogger.ProcessingContext.ErrorInfo errorInfo = context.getErrors().get("test_error");
        assertEquals("test_error", errorInfo.getErrorType());
        assertEquals("Test error message", errorInfo.getErrorMessage());
        assertEquals(testException, errorInfo.getThrowable());
    }

    @Test
    void testLogProcessingComplete() {
        // Given
        BufferedImage image = createTestImage(100, 100, "Test");
        String requestId = ocrLogger.startProcessing(image, "test.png");
        OcrResult result = OcrResult.success("Test text", 85.5f);

        // When
        ocrLogger.logProcessingComplete(requestId, result);

        // Then
        assertEquals(0, ocrLogger.getActiveContextCount()); // Context should be removed
        
        // Context should still be accessible during the method call
        assertDoesNotThrow(() -> ocrLogger.logProcessingComplete(requestId, result));
    }

    @Test
    void testLogCacheOperation() {
        // Given
        BufferedImage image = createTestImage(100, 100, "Test");
        String requestId = ocrLogger.startProcessing(image, "test.png");

        // When & Then - Should not throw exceptions
        assertDoesNotThrow(() -> ocrLogger.logCacheOperation(requestId, "get", true, "cache_key_123"));
        assertDoesNotThrow(() -> ocrLogger.logCacheOperation(requestId, "put", false, "cache_key_456"));
    }

    @Test
    void testLogConfiguration() {
        // Given
        BufferedImage image = createTestImage(100, 100, "Test");
        String requestId = ocrLogger.startProcessing(image, "test.png");
        Map<String, Object> config = Map.of(
            "language", "eng",
            "psm", 6,
            "oem", 3
        );

        // When & Then - Should not throw exceptions
        assertDoesNotThrow(() -> ocrLogger.logConfiguration(requestId, config));
    }

    @Test
    void testLogImagePreprocessing() {
        // Given
        BufferedImage image = createTestImage(100, 100, "Test");
        String requestId = ocrLogger.startProcessing(image, "test.png");

        // When & Then - Should not throw exceptions
        assertDoesNotThrow(() -> ocrLogger.logImagePreprocessing(requestId, "resize", 200, 200, 100, 100));
    }

    @Test
    void testProcessingContextLifecycle() {
        // Given
        BufferedImage image = createTestImage(100, 100, "Test");

        // When - Start processing
        String requestId = ocrLogger.startProcessing(image, "test.png");
        assertEquals(1, ocrLogger.getActiveContextCount());

        // Add some stages
        ocrLogger.logProcessingStage(requestId, "stage1", "First stage");
        ocrLogger.logProcessingStage(requestId, "stage2", "Second stage");

        // Add an error
        ocrLogger.logError(requestId, "test_error", "Test error", null, Map.of());

        // Complete processing
        OcrResult result = OcrResult.success("Test text", 85.0f);
        ocrLogger.logProcessingComplete(requestId, result);

        // Then
        assertEquals(0, ocrLogger.getActiveContextCount()); // Context removed after completion
    }

    @Test
    void testProcessingContextDetails() {
        // Given
        BufferedImage image = createTestImage(200, 150, "Test");
        String requestId = ocrLogger.startProcessing(image, "detailed_test.png");

        // When
        ocrLogger.logProcessingStage(requestId, "preprocessing", "Image preprocessing");
        ocrLogger.logProcessingStage(requestId, "ocr", "OCR recognition");
        ocrLogger.logError(requestId, "warning", "Low confidence", null, Map.of());

        // Then
        OcrLogger.ProcessingContext context = ocrLogger.getContext(requestId);
        assertNotNull(context);
        assertEquals(requestId, context.getRequestId());
        assertEquals("detailed_test.png", context.getImageName());
        assertEquals("buffered", context.getImageType());
        assertEquals(2, context.getStageCount());
        assertTrue(context.hasErrors());
        assertNotNull(context.getStartTime());
        assertNull(context.getEndTime()); // Not completed yet
        assertTrue(context.getTotalDurationMs() >= 0);
    }

    @Test
    void testMultipleContexts() {
        // Given
        BufferedImage image1 = createTestImage(100, 100, "Test1");
        BufferedImage image2 = createTestImage(200, 200, "Test2");

        // When
        String requestId1 = ocrLogger.startProcessing(image1, "test1.png");
        String requestId2 = ocrLogger.startProcessing(image2, "test2.png");

        // Then
        assertEquals(2, ocrLogger.getActiveContextCount());
        assertNotEquals(requestId1, requestId2);
        
        OcrLogger.ProcessingContext context1 = ocrLogger.getContext(requestId1);
        OcrLogger.ProcessingContext context2 = ocrLogger.getContext(requestId2);
        
        assertNotNull(context1);
        assertNotNull(context2);
        assertEquals("test1.png", context1.getImageName());
        assertEquals("test2.png", context2.getImageName());
    }

    @Test
    void testInvalidRequestId() {
        // Given
        String invalidRequestId = "invalid_request_id";

        // When & Then - Should handle gracefully
        assertDoesNotThrow(() -> ocrLogger.logProcessingStage(invalidRequestId, "stage", "details"));
        assertDoesNotThrow(() -> ocrLogger.logError(invalidRequestId, "error", "message", null, Map.of()));
        assertDoesNotThrow(() -> ocrLogger.logPerformance(invalidRequestId, "op", 100, Map.of()));
        
        assertNull(ocrLogger.getContext(invalidRequestId));
    }

    @Test
    void testStageInfo() {
        // Given
        String stage = "test_stage";
        String details = "test details";
        
        // When
        OcrLogger.ProcessingContext.StageInfo stageInfo = 
            new OcrLogger.ProcessingContext.StageInfo(stage, details, java.time.LocalDateTime.now());

        // Then
        assertEquals(stage, stageInfo.getStage());
        assertEquals(details, stageInfo.getDetails());
        assertNotNull(stageInfo.getTimestamp());
    }

    @Test
    void testErrorInfo() {
        // Given
        String errorType = "test_error";
        String errorMessage = "test message";
        Exception exception = new RuntimeException("test");
        
        // When
        OcrLogger.ProcessingContext.ErrorInfo errorInfo = 
            new OcrLogger.ProcessingContext.ErrorInfo(errorType, errorMessage, exception, java.time.LocalDateTime.now());

        // Then
        assertEquals(errorType, errorInfo.getErrorType());
        assertEquals(errorMessage, errorInfo.getErrorMessage());
        assertEquals(exception, errorInfo.getThrowable());
        assertNotNull(errorInfo.getTimestamp());
    }

    // Helper method
    private BufferedImage createTestImage(int width, int height, String text) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // White background
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width, height);
        
        // Black text
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("Arial", Font.PLAIN, 12));
        
        FontMetrics fm = g2d.getFontMetrics();
        int x = (width - fm.stringWidth(text)) / 2;
        int y = (height + fm.getHeight()) / 2;
        
        g2d.drawString(text, x, y);
        g2d.dispose();
        
        return image;
    }
}
