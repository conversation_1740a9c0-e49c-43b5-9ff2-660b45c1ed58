package com.talkweb.ai.indexer.integration;

import com.talkweb.ai.indexer.config.ImageConversionOptions;
import com.talkweb.ai.indexer.config.OcrConfiguration;
import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.core.converter.ConversionContext;
import com.talkweb.ai.indexer.core.converter.ConversionOptions;
import com.talkweb.ai.indexer.core.impl.ImageToMarkdownConverter;
import com.talkweb.ai.indexer.model.OcrResult;
import com.talkweb.ai.indexer.logging.OcrLogger;
import com.talkweb.ai.indexer.metrics.OcrMetrics;
import com.talkweb.ai.indexer.service.OcrCacheManager;
import com.talkweb.ai.indexer.service.OcrService;
import com.talkweb.ai.indexer.service.OcrThreadPoolManager;
import com.talkweb.ai.indexer.util.PerformanceMonitor;
import com.talkweb.ai.indexer.util.image.ImagePreprocessor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 图像转换器集成测试
 * 
 * 这些测试需要Tesseract OCR引擎正确安装才能运行。
 * 使用系统属性 -Dtesseract.available=true 来启用这些测试。
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@SpringBootTest
@TestPropertySource(properties = {
    "app.ocr.enabled=true",
    "app.ocr.languages[0]=eng",
    "app.ocr.languages[1]=chi_sim",
    "app.ocr.confidence-threshold=50",
    "app.ocr.timeout-seconds=30"
})
@EnabledIfSystemProperty(named = "tesseract.available", matches = "true")
class ImageConverterIntegrationTest {
    
    private OcrService ocrService;
    private ImagePreprocessor imagePreprocessor;
    private ImageToMarkdownConverter converter;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        // Create OCR configuration
        OcrConfiguration ocrConfig = new OcrConfiguration();
        ocrConfig.setEnabled(true);
        ocrConfig.setLanguages(List.of("eng", "chi_sim"));
        ocrConfig.setConfidenceThreshold(50);
        ocrConfig.setTimeoutSeconds(30);
        
        // Initialize services
        OcrThreadPoolManager threadPoolManager = new OcrThreadPoolManager(ocrConfig);
        OcrCacheManager cacheManager = new OcrCacheManager(ocrConfig);
        PerformanceMonitor performanceMonitor = new PerformanceMonitor();
        OcrMetrics ocrMetrics = new OcrMetrics();
        OcrLogger ocrLogger = new OcrLogger();

        // Initialize managers
        threadPoolManager.initialize();
        cacheManager.initialize();

        ocrService = new OcrService(ocrConfig, threadPoolManager, cacheManager, performanceMonitor, ocrMetrics, ocrLogger);
        imagePreprocessor = new ImagePreprocessor();
        converter = new ImageToMarkdownConverter(ocrService, imagePreprocessor);
        
        // Initialize OCR service
        try {
            ocrService.initialize();
        } catch (Exception e) {
            // Skip tests if Tesseract is not available
            org.junit.jupiter.api.Assumptions.assumeTrue(false, "Tesseract OCR not available: " + e.getMessage());
        }
    }
    
    @Test
    void testOcrServiceAvailability() {
        assertTrue(ocrService.isAvailable(), "OCR service should be available for integration tests");
    }
    
    @Test
    void testSimpleTextRecognition() throws IOException {
        // Create an image with simple text
        BufferedImage testImage = createTextImage("Hello World", 200, 100);
        
        // Perform OCR
        OcrResult result = ocrService.recognizeText(testImage);
        
        assertNotNull(result);
        assertTrue(result.isSuccess() || result.getStatus() == OcrResult.Status.LOW_CONFIDENCE);
        assertNotNull(result.getText());
        
        // The text should contain some recognizable content
        String recognizedText = result.getText().toLowerCase();
        assertTrue(recognizedText.length() > 0, "Should recognize some text");
        
        System.out.println("Recognized text: '" + result.getText() + "'");
        System.out.println("Confidence: " + result.getConfidence() + "%");
    }
    
    @Test
    void testImagePreprocessing() {
        // Create a test image
        BufferedImage testImage = createTextImage("Test Content", 150, 80);
        
        // Test preprocessing
        ImagePreprocessor.PreprocessingOptions options = new ImagePreprocessor.PreprocessingOptions();
        ImagePreprocessor.PreprocessingResult result = imagePreprocessor.preprocessImage(testImage, options);
        
        assertTrue(result.isSuccess());
        assertNotNull(result.getProcessedImage());
        assertNotNull(result.getProcessingInfo());
        
        // Check that processing info contains expected keys
        assertTrue(result.getProcessingInfo().containsKey("originalWidth"));
        assertTrue(result.getProcessingInfo().containsKey("originalHeight"));
    }
    
    @Test
    void testCompleteImageToMarkdownConversion() throws Exception {
        // Create a test image file with text
        BufferedImage testImage = createComplexTextImage();
        File imageFile = tempDir.resolve("test_document.png").toFile();
        ImageIO.write(testImage, "png", imageFile);
        
        // Create conversion context
        ConversionContext context = createConversionContext();
        
        // Perform conversion
        ConversionResult result = converter.convert(imageFile, context);
        
        assertNotNull(result);
        assertNotNull(result.getContent());
        assertTrue(result.getMessage().contains(".md") || result.getContent().length() > 0);
        
        // Check that the markdown contains expected structure
        String markdown = result.getContent();
        assertFalse(markdown.isEmpty());
        
        System.out.println("Generated Markdown:");
        System.out.println("==================");
        System.out.println(markdown);
        System.out.println("==================");
        
        // The markdown should contain some text content or indicate no text was found
        assertTrue(markdown.contains("test_document") || 
                  markdown.contains("No text content detected") ||
                  markdown.length() > 50);
    }
    
    @Test
    void testHighQualityConversion() throws Exception {
        // Create a high-quality test image
        BufferedImage testImage = createHighQualityTextImage();
        File imageFile = tempDir.resolve("high_quality.png").toFile();
        ImageIO.write(testImage, "png", imageFile);
        
        // Use high-quality conversion options
        ImageConversionOptions options = ImageConversionOptions.createHighQuality();
        ConversionContext context = createConversionContext(options);
        
        // Perform conversion
        ConversionResult result = converter.convert(imageFile, context);
        
        assertNotNull(result);
        assertNotNull(result.getContent());
        
        String markdown = result.getContent();
        System.out.println("High-quality conversion result:");
        System.out.println(markdown);
        
        // High-quality conversion should include metadata
        assertTrue(markdown.contains("**Source:**") || markdown.length() > 0);
    }
    
    @Test
    void testMultiLanguageText() throws Exception {
        // Create an image with mixed English and Chinese text (if supported)
        BufferedImage testImage = createMultiLanguageImage();
        File imageFile = tempDir.resolve("multilang.png").toFile();
        ImageIO.write(testImage, "png", imageFile);
        
        ConversionContext context = createConversionContext();
        ConversionResult result = converter.convert(imageFile, context);
        
        assertNotNull(result);
        assertNotNull(result.getContent());
        
        System.out.println("Multi-language conversion result:");
        System.out.println(result.getContent());
    }
    
    @Test
    void testAsyncConversion() throws Exception {
        // Create test image
        BufferedImage testImage = createTextImage("Async Test", 200, 100);
        
        // Test async OCR
        var future = ocrService.recognizeTextAsync(testImage);
        assertNotNull(future);
        
        OcrResult result = future.get();
        assertNotNull(result);
        
        System.out.println("Async OCR result: " + result.getText());
    }
    
    @Test
    void testErrorHandling() {
        // Test with non-existent file
        File nonExistentFile = new File("non_existent.png");
        ConversionContext context = createConversionContext();
        
        assertThrows(Exception.class, () -> {
            converter.convert(nonExistentFile, context);
        });
    }
    
    // Helper methods
    
    private BufferedImage createTextImage(String text, int width, int height) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // Enable anti-aliasing for better text quality
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        
        // Fill with white background
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width, height);
        
        // Draw black text
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("Arial", Font.PLAIN, 16));
        
        FontMetrics fm = g2d.getFontMetrics();
        int textWidth = fm.stringWidth(text);
        int textHeight = fm.getHeight();
        
        int x = (width - textWidth) / 2;
        int y = (height + textHeight) / 2;
        
        g2d.drawString(text, x, y);
        g2d.dispose();
        
        return image;
    }
    
    private BufferedImage createComplexTextImage() {
        BufferedImage image = new BufferedImage(400, 300, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        
        // White background
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, 400, 300);
        
        // Black text
        g2d.setColor(Color.BLACK);
        
        // Title
        g2d.setFont(new Font("Arial", Font.BOLD, 20));
        g2d.drawString("Document Title", 50, 50);
        
        // Content
        g2d.setFont(new Font("Arial", Font.PLAIN, 14));
        g2d.drawString("This is a test document with multiple lines.", 50, 100);
        g2d.drawString("It contains various text elements.", 50, 130);
        g2d.drawString("OCR should be able to recognize this content.", 50, 160);
        
        // Some numbers
        g2d.drawString("Numbers: 123 456 789", 50, 200);
        
        g2d.dispose();
        return image;
    }
    
    private BufferedImage createHighQualityTextImage() {
        BufferedImage image = new BufferedImage(600, 400, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        
        // White background
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, 600, 400);
        
        // Black text with high quality
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("Times New Roman", Font.PLAIN, 18));
        
        String[] lines = {
            "High Quality Document",
            "",
            "This document is designed for high-quality OCR testing.",
            "It uses clear fonts and proper spacing.",
            "The text should be easily recognizable.",
            "",
            "Key Features:",
            "- Clear typography",
            "- Good contrast",
            "- Proper spacing",
            "- Multiple paragraphs"
        };
        
        int y = 50;
        for (String line : lines) {
            if (!line.isEmpty()) {
                g2d.drawString(line, 50, y);
            }
            y += 25;
        }
        
        g2d.dispose();
        return image;
    }
    
    private BufferedImage createMultiLanguageImage() {
        BufferedImage image = new BufferedImage(400, 200, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        
        // White background
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, 400, 200);
        
        // Black text
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("Arial", Font.PLAIN, 16));
        
        // English text
        g2d.drawString("English: Hello World", 50, 50);
        
        // Chinese text (if font supports it)
        g2d.drawString("Chinese: 你好世界", 50, 100);
        
        // Numbers and symbols
        g2d.drawString("Numbers: 123 456", 50, 150);
        
        g2d.dispose();
        return image;
    }
    
    private ConversionContext createConversionContext() {
        return createConversionContext(ImageConversionOptions.createDefault());
    }
    
    private ConversionContext createConversionContext(ImageConversionOptions imageOptions) {
        ConversionOptions options = ConversionOptions.builder()
                .option("imageConversionOptions", imageOptions)
                .build();

        return ConversionContext.builder()
                .mode("test")
                .options(options)
                .build();
    }
}
