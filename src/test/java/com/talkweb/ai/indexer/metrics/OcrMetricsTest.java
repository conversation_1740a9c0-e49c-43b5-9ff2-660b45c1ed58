package com.talkweb.ai.indexer.metrics;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OCR指标测试类
 */
class OcrMetricsTest {

    private OcrMetrics ocrMetrics;
    private MeterRegistry meterRegistry;

    @BeforeEach
    void setUp() {
        meterRegistry = new SimpleMeterRegistry();
        ocrMetrics = new OcrMetrics();
        ocrMetrics.bindTo(meterRegistry);
    }

    @Test
    void testStartProcessing() {
        // When
        Timer.Sample sample = ocrMetrics.startProcessing();

        // Then
        assertNotNull(sample);
    }

    @Test
    void testRecordProcessingSuccess() {
        // Given
        Timer.Sample sample = ocrMetrics.startProcessing();
        double confidence = 85.5;
        int imageSize = 1024 * 768;

        // When
        ocrMetrics.recordProcessingSuccess(sample, confidence, imageSize);

        // Then
        OcrMetrics.MetricsSummary summary = ocrMetrics.getMetricsSummary();
        assertEquals(1, summary.getTotalProcessed());
        assertEquals(1, summary.getTotalSuccessful());
        assertEquals(0, summary.getTotalErrors());
        assertEquals(1.0, summary.getSuccessRate(), 0.01);
        assertEquals(0.0, summary.getErrorRate(), 0.01);
    }

    @Test
    void testRecordProcessingSuccessWithImageType() {
        // Given
        Timer.Sample sample = ocrMetrics.startProcessing();
        double confidence = 90.0;
        int imageSize = 800 * 600;
        String imageType = "png";

        // When
        ocrMetrics.recordProcessingSuccess(sample, confidence, imageSize, imageType);

        // Then
        OcrMetrics.MetricsSummary summary = ocrMetrics.getMetricsSummary();
        assertEquals(1, summary.getTotalProcessed());
        assertEquals(1, summary.getTotalSuccessful());
        assertEquals(1.0, summary.getSuccessRate(), 0.01);
    }

    @Test
    void testRecordProcessingError() {
        // Given
        Timer.Sample sample = ocrMetrics.startProcessing();
        String errorType = "tesseract_error";

        // When
        ocrMetrics.recordProcessingError(sample, errorType);

        // Then
        OcrMetrics.MetricsSummary summary = ocrMetrics.getMetricsSummary();
        assertEquals(1, summary.getTotalProcessed());
        assertEquals(0, summary.getTotalSuccessful());
        assertEquals(1, summary.getTotalErrors());
        assertEquals(0.0, summary.getSuccessRate(), 0.01);
        assertEquals(1.0, summary.getErrorRate(), 0.01);
    }

    @Test
    void testRecordCacheHitAndMiss() {
        // When
        ocrMetrics.recordCacheHit();
        ocrMetrics.recordCacheHit();
        ocrMetrics.recordCacheMiss();

        // Then
        double hitRate = ocrMetrics.getCacheHitRate();
        assertEquals(0.67, hitRate, 0.01); // 2 hits out of 3 total requests
    }

    @Test
    void testUpdateThreadPoolStats() {
        // Given
        long activeThreads = 5;
        long queueSize = 10;

        // When
        ocrMetrics.updateThreadPoolStats(activeThreads, queueSize);

        // Then
        OcrMetrics.MetricsSummary summary = ocrMetrics.getMetricsSummary();
        assertEquals(activeThreads, summary.getActiveThreads());
        assertEquals(queueSize, summary.getQueueSize());
    }

    @Test
    void testRecordBatchProcessing() {
        // Given
        int totalItems = 100;
        int successfulItems = 85;
        int failedItems = 15;
        long processingTimeMs = 5000;

        // When
        ocrMetrics.recordBatchProcessing(totalItems, successfulItems, failedItems, processingTimeMs);

        // Then - verify that the method completes without error
        // The actual metrics are registered as gauges, so we can't easily verify their values in tests
        assertDoesNotThrow(() -> ocrMetrics.recordBatchProcessing(totalItems, successfulItems, failedItems, processingTimeMs));
    }

    @Test
    void testGetSuccessRate() {
        // Given - no processing yet
        assertEquals(0.0, ocrMetrics.getSuccessRate(), 0.01);

        // When - record some successes and failures
        Timer.Sample sample1 = ocrMetrics.startProcessing();
        ocrMetrics.recordProcessingSuccess(sample1, 85.0, 1000);

        Timer.Sample sample2 = ocrMetrics.startProcessing();
        ocrMetrics.recordProcessingSuccess(sample2, 90.0, 1200);

        Timer.Sample sample3 = ocrMetrics.startProcessing();
        ocrMetrics.recordProcessingError(sample3, "error");

        // Then
        assertEquals(0.67, ocrMetrics.getSuccessRate(), 0.01); // 2 successes out of 3 total
    }

    @Test
    void testGetErrorRate() {
        // Given - no processing yet
        assertEquals(0.0, ocrMetrics.getErrorRate(), 0.01);

        // When - record some successes and failures
        Timer.Sample sample1 = ocrMetrics.startProcessing();
        ocrMetrics.recordProcessingSuccess(sample1, 85.0, 1000);

        Timer.Sample sample2 = ocrMetrics.startProcessing();
        ocrMetrics.recordProcessingError(sample2, "error1");

        Timer.Sample sample3 = ocrMetrics.startProcessing();
        ocrMetrics.recordProcessingError(sample3, "error2");

        // Then
        assertEquals(0.67, ocrMetrics.getErrorRate(), 0.01); // 2 errors out of 3 total
    }

    @Test
    void testGetCacheHitRate() {
        // Given - no cache requests yet
        assertEquals(0.0, ocrMetrics.getCacheHitRate(), 0.01);

        // When - record cache hits and misses
        ocrMetrics.recordCacheHit();
        ocrMetrics.recordCacheHit();
        ocrMetrics.recordCacheHit();
        ocrMetrics.recordCacheMiss();

        // Then
        assertEquals(0.75, ocrMetrics.getCacheHitRate(), 0.01); // 3 hits out of 4 total
    }

    @Test
    void testGetThroughput() {
        // Given
        Timer.Sample sample1 = ocrMetrics.startProcessing();
        Timer.Sample sample2 = ocrMetrics.startProcessing();

        // When
        ocrMetrics.recordProcessingSuccess(sample1, 85.0, 1000);
        ocrMetrics.recordProcessingSuccess(sample2, 90.0, 1200);

        // Then
        double throughput = ocrMetrics.getThroughput();
        assertTrue(throughput >= 0.0); // Should be non-negative
    }

    @Test
    void testGetMetricsSummary() {
        // Given
        Timer.Sample sample1 = ocrMetrics.startProcessing();
        Timer.Sample sample2 = ocrMetrics.startProcessing();
        Timer.Sample sample3 = ocrMetrics.startProcessing();

        ocrMetrics.recordProcessingSuccess(sample1, 85.0, 1000);
        ocrMetrics.recordProcessingSuccess(sample2, 90.0, 1200);
        ocrMetrics.recordProcessingError(sample3, "error");

        ocrMetrics.recordCacheHit();
        ocrMetrics.recordCacheMiss();

        ocrMetrics.updateThreadPoolStats(3, 5);

        // When
        OcrMetrics.MetricsSummary summary = ocrMetrics.getMetricsSummary();

        // Then
        assertNotNull(summary);
        assertEquals(3, summary.getTotalProcessed());
        assertEquals(2, summary.getTotalSuccessful());
        assertEquals(1, summary.getTotalErrors());
        assertEquals(0.67, summary.getSuccessRate(), 0.01);
        assertEquals(0.33, summary.getErrorRate(), 0.01);
        assertEquals(0.5, summary.getCacheHitRate(), 0.01);
        assertEquals(3, summary.getActiveThreads());
        assertEquals(5, summary.getQueueSize());
        assertTrue(summary.getAvgProcessingTimeMs() >= 0);
        assertTrue(summary.getThroughput() >= 0);
    }

    @Test
    void testMetricsSummaryToString() {
        // Given
        Timer.Sample sample = ocrMetrics.startProcessing();
        ocrMetrics.recordProcessingSuccess(sample, 85.0, 1000);
        ocrMetrics.updateThreadPoolStats(2, 3);

        // When
        OcrMetrics.MetricsSummary summary = ocrMetrics.getMetricsSummary();
        String summaryString = summary.toString();

        // Then
        assertNotNull(summaryString);
        assertTrue(summaryString.contains("processed=1"));
        assertTrue(summaryString.contains("successful=1"));
        assertTrue(summaryString.contains("errors=0"));
        assertTrue(summaryString.contains("activeThreads=2"));
        assertTrue(summaryString.contains("queueSize=3"));
    }

    @Test
    void testMultipleErrorTypes() {
        // Given
        Timer.Sample sample1 = ocrMetrics.startProcessing();
        Timer.Sample sample2 = ocrMetrics.startProcessing();
        Timer.Sample sample3 = ocrMetrics.startProcessing();

        // When - record different error types
        ocrMetrics.recordProcessingError(sample1, "tesseract_error");
        ocrMetrics.recordProcessingError(sample2, "timeout_error");
        ocrMetrics.recordProcessingError(sample3, "tesseract_error");

        // Then
        OcrMetrics.MetricsSummary summary = ocrMetrics.getMetricsSummary();
        assertEquals(3, summary.getTotalErrors());
        assertEquals(1.0, summary.getErrorRate(), 0.01);
    }

    @Test
    void testMixedProcessingResults() {
        // Given
        int successCount = 7;
        int errorCount = 3;

        // When - record mixed results
        for (int i = 0; i < successCount; i++) {
            Timer.Sample sample = ocrMetrics.startProcessing();
            ocrMetrics.recordProcessingSuccess(sample, 80.0 + i, 1000 + i * 100);
        }

        for (int i = 0; i < errorCount; i++) {
            Timer.Sample sample = ocrMetrics.startProcessing();
            ocrMetrics.recordProcessingError(sample, "error_" + i);
        }

        // Then
        OcrMetrics.MetricsSummary summary = ocrMetrics.getMetricsSummary();
        assertEquals(successCount + errorCount, summary.getTotalProcessed());
        assertEquals(successCount, summary.getTotalSuccessful());
        assertEquals(errorCount, summary.getTotalErrors());
        assertEquals(0.7, summary.getSuccessRate(), 0.01);
        assertEquals(0.3, summary.getErrorRate(), 0.01);
    }
}
