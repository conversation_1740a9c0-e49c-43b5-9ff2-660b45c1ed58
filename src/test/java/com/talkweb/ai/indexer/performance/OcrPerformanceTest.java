package com.talkweb.ai.indexer.performance;

import com.talkweb.ai.indexer.config.OcrConfiguration;
import com.talkweb.ai.indexer.logging.OcrLogger;
import com.talkweb.ai.indexer.metrics.OcrMetrics;
import com.talkweb.ai.indexer.model.OcrResult;
import com.talkweb.ai.indexer.service.OcrCacheManager;
import com.talkweb.ai.indexer.service.OcrService;
import com.talkweb.ai.indexer.service.OcrThreadPoolManager;
import com.talkweb.ai.indexer.util.PerformanceMonitor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OCR性能基准测试类
 * 
 * 用于测试OCR服务在不同场景下的性能表现，包括：
 * - 不同图像尺寸的处理性能
 * - 并发处理能力
 * - 缓存效果
 * - 内存使用情况
 * 
 * 注意：这些测试需要Tesseract环境，通过系统属性启用：
 * -Dperformance.test.enabled=true
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@EnabledIfSystemProperty(named = "performance.test.enabled", matches = "true")
public class OcrPerformanceTest {
    
    private static final Logger logger = LoggerFactory.getLogger(OcrPerformanceTest.class);
    
    private OcrService ocrService;
    private OcrConfiguration ocrConfig;
    private OcrThreadPoolManager threadPoolManager;
    private OcrCacheManager cacheManager;
    private PerformanceMonitor performanceMonitor;
    private OcrMetrics ocrMetrics;
    private OcrLogger ocrLogger;
    
    // 性能基准阈值
    private static final long SMALL_IMAGE_MAX_TIME_MS = 5000;  // 小图像最大处理时间
    private static final long MEDIUM_IMAGE_MAX_TIME_MS = 10000; // 中等图像最大处理时间
    private static final long LARGE_IMAGE_MAX_TIME_MS = 20000;  // 大图像最大处理时间
    
    @BeforeEach
    void setUp() {
        // 创建性能优化的配置
        ocrConfig = createPerformanceOptimizedConfig();
        threadPoolManager = new OcrThreadPoolManager(ocrConfig);
        cacheManager = new OcrCacheManager(ocrConfig);
        performanceMonitor = new PerformanceMonitor();
        ocrMetrics = new OcrMetrics();
        ocrLogger = new OcrLogger();

        // 初始化管理器
        threadPoolManager.initialize();
        cacheManager.initialize();

        ocrService = new OcrService(ocrConfig, threadPoolManager, cacheManager, performanceMonitor, ocrMetrics, ocrLogger);
        
        try {
            ocrService.initialize();
            logger.info("OCR service initialized for performance testing");
        } catch (Exception e) {
            logger.warn("OCR service initialization failed, tests may be skipped: {}", e.getMessage());
        }
    }
    
    @Test
    void testSmallImagePerformance() {
        if (!ocrService.isAvailable()) {
            logger.warn("OCR service not available, skipping small image performance test");
            return;
        }
        
        logger.info("Starting small image performance test");
        
        // 创建小图像 (200x100)
        BufferedImage smallImage = createTestImage(200, 100, "Small Test Image");
        
        // 预热
        warmUp(smallImage, 3);
        
        // 性能测试
        long startTime = System.currentTimeMillis();
        OcrResult result = ocrService.recognizeText(smallImage);
        long processingTime = System.currentTimeMillis() - startTime;
        
        // 验证结果
        assertNotNull(result);
        assertTrue(processingTime < SMALL_IMAGE_MAX_TIME_MS, 
                  String.format("Small image processing took %dms, expected < %dms", 
                               processingTime, SMALL_IMAGE_MAX_TIME_MS));
        
        logger.info("Small image performance: {}ms (threshold: {}ms)", 
                   processingTime, SMALL_IMAGE_MAX_TIME_MS);
    }
    
    @Test
    void testMediumImagePerformance() {
        if (!ocrService.isAvailable()) {
            logger.warn("OCR service not available, skipping medium image performance test");
            return;
        }
        
        logger.info("Starting medium image performance test");
        
        // 创建中等图像 (800x600)
        BufferedImage mediumImage = createTestImage(800, 600, "Medium Test Image with More Text Content");
        
        // 预热
        warmUp(mediumImage, 2);
        
        // 性能测试
        long startTime = System.currentTimeMillis();
        OcrResult result = ocrService.recognizeText(mediumImage);
        long processingTime = System.currentTimeMillis() - startTime;
        
        // 验证结果
        assertNotNull(result);
        assertTrue(processingTime < MEDIUM_IMAGE_MAX_TIME_MS, 
                  String.format("Medium image processing took %dms, expected < %dms", 
                               processingTime, MEDIUM_IMAGE_MAX_TIME_MS));
        
        logger.info("Medium image performance: {}ms (threshold: {}ms)", 
                   processingTime, MEDIUM_IMAGE_MAX_TIME_MS);
    }
    
    @Test
    void testLargeImagePerformance() {
        if (!ocrService.isAvailable()) {
            logger.warn("OCR service not available, skipping large image performance test");
            return;
        }
        
        logger.info("Starting large image performance test");
        
        // 创建大图像 (1920x1080)
        BufferedImage largeImage = createTestImage(1920, 1080, 
            "Large Test Image with Extensive Text Content for Performance Testing");
        
        // 预热
        warmUp(largeImage, 1);
        
        // 性能测试
        long startTime = System.currentTimeMillis();
        OcrResult result = ocrService.recognizeText(largeImage);
        long processingTime = System.currentTimeMillis() - startTime;
        
        // 验证结果
        assertNotNull(result);
        assertTrue(processingTime < LARGE_IMAGE_MAX_TIME_MS, 
                  String.format("Large image processing took %dms, expected < %dms", 
                               processingTime, LARGE_IMAGE_MAX_TIME_MS));
        
        logger.info("Large image performance: {}ms (threshold: {}ms)", 
                   processingTime, LARGE_IMAGE_MAX_TIME_MS);
    }
    
    @Test
    void testConcurrentProcessingPerformance() {
        if (!ocrService.isAvailable()) {
            logger.warn("OCR service not available, skipping concurrent processing test");
            return;
        }
        
        logger.info("Starting concurrent processing performance test");
        
        int concurrentTasks = 10;
        List<BufferedImage> testImages = new ArrayList<>();
        
        // 创建测试图像
        for (int i = 0; i < concurrentTasks; i++) {
            testImages.add(createTestImage(400, 300, "Concurrent Test " + i));
        }
        
        // 并发性能测试
        long startTime = System.currentTimeMillis();
        
        List<CompletableFuture<OcrResult>> futures = new ArrayList<>();
        for (BufferedImage image : testImages) {
            futures.add(ocrService.recognizeTextAsync(image));
        }
        
        // 等待所有任务完成
        CompletableFuture<Void> allTasks = CompletableFuture.allOf(
            futures.toArray(new CompletableFuture[0]));
        
        try {
            allTasks.get(60, TimeUnit.SECONDS); // 60秒超时
        } catch (Exception e) {
            fail("Concurrent processing failed: " + e.getMessage());
        }
        
        long totalTime = System.currentTimeMillis() - startTime;
        double averageTime = (double) totalTime / concurrentTasks;
        
        // 验证结果
        for (CompletableFuture<OcrResult> future : futures) {
            assertTrue(future.isDone());
            assertNotNull(future.join());
        }
        
        logger.info("Concurrent processing performance: {} tasks in {}ms, average: {:.2f}ms per task", 
                   concurrentTasks, totalTime, averageTime);
        
        // 检查线程池统计
        var threadPoolStats = ocrService.getThreadPoolStats();
        logger.info("Thread pool stats: {}", threadPoolStats);
        
        assertTrue(threadPoolStats.getTotalTasksCompleted() >= concurrentTasks);
    }
    
    @Test
    void testCachePerformance() {
        if (!ocrService.isAvailable()) {
            logger.warn("OCR service not available, skipping cache performance test");
            return;
        }
        
        logger.info("Starting cache performance test");
        
        BufferedImage testImage = createTestImage(600, 400, "Cache Performance Test");
        
        // 第一次处理（无缓存）
        long startTime1 = System.currentTimeMillis();
        OcrResult result1 = ocrService.recognizeText(testImage);
        long firstProcessingTime = System.currentTimeMillis() - startTime1;
        
        // 第二次处理（有缓存）
        long startTime2 = System.currentTimeMillis();
        OcrResult result2 = ocrService.recognizeText(testImage);
        long secondProcessingTime = System.currentTimeMillis() - startTime2;
        
        // 验证结果
        assertNotNull(result1);
        assertNotNull(result2);
        
        // 缓存应该显著提高性能
        assertTrue(secondProcessingTime < firstProcessingTime / 2, 
                  String.format("Cache didn't improve performance significantly. " +
                               "First: %dms, Second: %dms", firstProcessingTime, secondProcessingTime));
        
        logger.info("Cache performance: First processing: {}ms, Cached processing: {}ms, " +
                   "Improvement: {:.2f}x", firstProcessingTime, secondProcessingTime, 
                   (double) firstProcessingTime / secondProcessingTime);
        
        // 检查缓存统计
        var cacheStats = ocrService.getCacheStats();
        logger.info("Cache stats: {}", cacheStats);
        
        assertTrue(cacheStats.getHits() > 0);
    }
    
    @Test
    void testMemoryUsageUnderLoad() {
        if (!ocrService.isAvailable()) {
            logger.warn("OCR service not available, skipping memory usage test");
            return;
        }
        
        logger.info("Starting memory usage under load test");
        
        Runtime runtime = Runtime.getRuntime();
        
        // 记录初始内存使用
        System.gc(); // 建议垃圾回收
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 处理多个图像
        int imageCount = 20;
        for (int i = 0; i < imageCount; i++) {
            BufferedImage testImage = createTestImage(500, 400, "Memory Test " + i);
            OcrResult result = ocrService.recognizeText(testImage);
            assertNotNull(result);
            
            // 每5个图像检查一次内存
            if (i % 5 == 0) {
                long currentMemory = runtime.totalMemory() - runtime.freeMemory();
                long memoryIncrease = currentMemory - initialMemory;
                logger.debug("Processed {} images, memory increase: {} MB", 
                           i + 1, memoryIncrease / 1024 / 1024);
            }
        }
        
        // 最终内存检查
        System.gc(); // 建议垃圾回收
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        long totalMemoryIncrease = finalMemory - initialMemory;
        
        logger.info("Memory usage test completed. Processed {} images, " +
                   "memory increase: {} MB", imageCount, totalMemoryIncrease / 1024 / 1024);
        
        // 内存增长应该在合理范围内（小于500MB）
        assertTrue(totalMemoryIncrease < 500 * 1024 * 1024, 
                  String.format("Memory usage increased by %d MB, which is too high", 
                               totalMemoryIncrease / 1024 / 1024));
    }
    
    // 辅助方法
    
    private OcrConfiguration createPerformanceOptimizedConfig() {
        OcrConfiguration config = new OcrConfiguration();
        config.setEnabled(true);
        config.setLanguages(List.of("eng"));
        config.setPageSegmentationMode(6); // SINGLE_BLOCK
        config.setOcrEngineMode(1); // NEURAL_NETS_LSTM_ONLY
        config.setConfidenceThreshold(50);
        config.setHighQualityThreshold(80);
        config.setTimeoutSeconds(30);
        config.setAdaptivePageSegmentation(true);
        config.setMaxImageSize(2048);
        config.setImageCacheEnabled(true);
        config.setCacheSize(50);
        config.setThreadPoolSize(4); // 固定4个线程用于性能测试
        return config;
    }
    
    private BufferedImage createTestImage(int width, int height, String text) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // 设置白色背景
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width, height);
        
        // 设置黑色文字
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("Arial", Font.PLAIN, Math.max(12, height / 20)));
        
        // 绘制文字
        FontMetrics fm = g2d.getFontMetrics();
        int x = (width - fm.stringWidth(text)) / 2;
        int y = height / 2;
        g2d.drawString(text, x, y);
        
        g2d.dispose();
        return image;
    }
    
    private void warmUp(BufferedImage image, int iterations) {
        logger.debug("Warming up with {} iterations", iterations);
        for (int i = 0; i < iterations; i++) {
            ocrService.recognizeText(image);
        }
    }
}
