package com.talkweb.ai.indexer.service;

import com.talkweb.ai.indexer.config.OcrConfiguration;
import com.talkweb.ai.indexer.metrics.OcrMetrics;
import com.talkweb.ai.indexer.model.OcrResult;
import com.talkweb.ai.indexer.util.PerformanceMonitor;
import io.micrometer.core.instrument.Timer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.*;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 批量OCR处理服务测试类
 */
@ExtendWith(MockitoExtension.class)
class BatchOcrProcessorTest {

    @Mock
    private OcrService ocrService;
    
    @Mock
    private OcrConfiguration ocrConfig;
    
    @Mock
    private PerformanceMonitor performanceMonitor;

    private BatchOcrProcessor batchProcessor;

    @BeforeEach
    void setUp() {
        // Mock performance monitor
        PerformanceMonitor.MeasurementContext mockContext = mock(PerformanceMonitor.MeasurementContext.class);
        lenient().when(performanceMonitor.startMeasurement(anyString())).thenReturn(mockContext);
        
        batchProcessor = new BatchOcrProcessor(ocrService, ocrConfig, performanceMonitor);
    }

    @Test
    void testProcessImagesWithDefaultOptions() {
        // Given
        Map<String, BufferedImage> images = createTestImages(5);
        BatchOcrProcessor.BatchProcessingOptions options = BatchOcrProcessor.BatchProcessingOptions.createDefault();
        
        // Mock OCR service to return successful results
        when(ocrService.recognizeText(any(BufferedImage.class)))
            .thenReturn(OcrResult.success("Test text", 85));

        // When
        BatchProcessingTask task = batchProcessor.processImages(images, options);

        // Then
        assertNotNull(task);
        assertEquals(5, task.getTotalItems());
        assertEquals(BatchProcessingTask.TaskStatus.PENDING, task.getStatus());
        
        // Wait for processing to complete
        waitForTaskCompletion(task, 10000);
        
        // Verify results
        assertTrue(task.isCompleted());
        assertEquals(BatchProcessingTask.TaskStatus.COMPLETED, task.getStatus());
        assertEquals(5, task.getProcessedCount());
        assertEquals(5, task.getSuccessCount());
        assertEquals(0, task.getFailureCount());
        
        verify(ocrService, times(5)).recognizeText(any(BufferedImage.class));
    }

    @Test
    void testProcessImagesWithProgressCallback() {
        // Given
        Map<String, BufferedImage> images = createTestImages(3);
        AtomicInteger progressCallbacks = new AtomicInteger(0);
        
        BatchOcrProcessor.BatchProcessingOptions options = BatchOcrProcessor.BatchProcessingOptions.createDefault();
        options.setProgressCallback(progress -> {
            progressCallbacks.incrementAndGet();
            assertNotNull(progress);
            assertTrue(progress.getProgressPercentage() >= 0);
            assertTrue(progress.getProgressPercentage() <= 100);
        });
        
        when(ocrService.recognizeText(any(BufferedImage.class)))
            .thenReturn(OcrResult.success("Test text", 85));

        // When
        BatchProcessingTask task = batchProcessor.processImages(images, options);
        waitForTaskCompletion(task, 10000);

        // Then
        assertTrue(task.isCompleted());
        assertTrue(progressCallbacks.get() > 0, "Progress callback should be called");
    }

    @Test
    void testProcessImagesWithFailures() {
        // Given
        Map<String, BufferedImage> images = createTestImages(4);
        BatchOcrProcessor.BatchProcessingOptions options = BatchOcrProcessor.BatchProcessingOptions.createDefault();
        
        // Mock OCR service to return mixed results
        when(ocrService.recognizeText(any(BufferedImage.class)))
            .thenReturn(OcrResult.success("Test text", 85))
            .thenReturn(OcrResult.failure("OCR failed"))
            .thenReturn(OcrResult.success("Test text", 90))
            .thenReturn(OcrResult.failure("Another failure"));

        // When
        BatchProcessingTask task = batchProcessor.processImages(images, options);
        waitForTaskCompletion(task, 10000);

        // Then
        assertTrue(task.isCompleted());
        assertEquals(4, task.getProcessedCount());
        assertEquals(2, task.getSuccessCount());
        assertEquals(2, task.getFailureCount());
        assertEquals(0.5, task.getSuccessRate(), 0.01);
    }

    @Test
    void testProcessImagesWithFailFast() {
        // Given
        Map<String, BufferedImage> images = createTestImages(5);
        BatchOcrProcessor.BatchProcessingOptions options = BatchOcrProcessor.BatchProcessingOptions.createDefault();
        options.setFailFast(true);
        
        // Mock OCR service to throw exception on second call
        when(ocrService.recognizeText(any(BufferedImage.class)))
            .thenReturn(OcrResult.success("Test text", 85))
            .thenThrow(new RuntimeException("OCR service error"));

        // When
        BatchProcessingTask task = batchProcessor.processImages(images, options);
        waitForTaskCompletion(task, 10000);

        // Then
        assertTrue(task.isCompleted());
        assertTrue(task.isCancelled() || task.getStatus() == BatchProcessingTask.TaskStatus.CANCELLED);
    }

    @Test
    void testBatchProcessingOptions() {
        // Test default options
        BatchOcrProcessor.BatchProcessingOptions defaultOptions = BatchOcrProcessor.BatchProcessingOptions.createDefault();
        assertEquals(10, defaultOptions.getBatchSize());
        assertEquals(100, defaultOptions.getBatchDelayMs());
        assertEquals(30, defaultOptions.getTimeoutMinutes());
        assertFalse(defaultOptions.isFailFast());

        // Test fast processing options
        BatchOcrProcessor.BatchProcessingOptions fastOptions = BatchOcrProcessor.BatchProcessingOptions.createFastProcessing();
        assertEquals(20, fastOptions.getBatchSize());
        assertEquals(50, fastOptions.getBatchDelayMs());
        assertEquals(15, fastOptions.getTimeoutMinutes());

        // Test robust processing options
        BatchOcrProcessor.BatchProcessingOptions robustOptions = BatchOcrProcessor.BatchProcessingOptions.createRobustProcessing();
        assertEquals(5, robustOptions.getBatchSize());
        assertEquals(200, robustOptions.getBatchDelayMs());
        assertEquals(60, robustOptions.getTimeoutMinutes());
        assertTrue(robustOptions.isEnableRetry());
        assertEquals(5, robustOptions.getMaxRetries());
    }

    @Test
    void testTaskManagement() {
        // Given
        Map<String, BufferedImage> images = createTestImages(3);
        BatchOcrProcessor.BatchProcessingOptions options = BatchOcrProcessor.BatchProcessingOptions.createDefault();
        
        when(ocrService.recognizeText(any(BufferedImage.class)))
            .thenReturn(OcrResult.success("Test text", 85));

        // When
        BatchProcessingTask task = batchProcessor.processImages(images, options);
        String taskId = task.getTaskId();

        // Then
        // Test task retrieval
        BatchProcessingTask retrievedTask = batchProcessor.getTask(taskId);
        assertNotNull(retrievedTask);
        assertEquals(taskId, retrievedTask.getTaskId());

        // Test active tasks list
        List<BatchProcessingTask> activeTasks = batchProcessor.getActiveTasks();
        assertTrue(activeTasks.contains(task));

        // Wait for completion
        waitForTaskCompletion(task, 10000);

        // Test task summary
        BatchProcessingTask.TaskSummary summary = task.getSummary();
        assertNotNull(summary);
        assertEquals(taskId, summary.getTaskId());
        assertEquals(3, summary.getTotalItems());
        assertTrue(summary.getProcessingTimeMs() > 0);
    }

    @Test
    void testTaskCancellation() {
        // Given
        Map<String, BufferedImage> images = createTestImages(10);
        BatchOcrProcessor.BatchProcessingOptions options = BatchOcrProcessor.BatchProcessingOptions.createDefault();
        options.setBatchDelayMs(1000); // Add delay to allow cancellation
        
        when(ocrService.recognizeText(any(BufferedImage.class)))
            .thenReturn(OcrResult.success("Test text", 85));

        // When
        BatchProcessingTask task = batchProcessor.processImages(images, options);
        String taskId = task.getTaskId();
        
        // Cancel the task after a short delay
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        boolean cancelled = batchProcessor.cancelTask(taskId);

        // Then
        assertTrue(cancelled);
        assertTrue(task.isCancelled());
        assertEquals(BatchProcessingTask.TaskStatus.CANCELLED, task.getStatus());
    }

    @Test
    void testBatchProgress() {
        // Test BatchProgress creation and methods
        BatchOcrProcessor.BatchProgress progress = new BatchOcrProcessor.BatchProgress(10, 7, 6, 1);
        
        assertEquals(10, progress.getTotalItems());
        assertEquals(7, progress.getProcessedItems());
        assertEquals(6, progress.getSuccessfulItems());
        assertEquals(1, progress.getFailedItems());
        assertEquals(70.0, progress.getProgressPercentage(), 0.01);
        
        String progressString = progress.toString();
        assertNotNull(progressString);
        assertTrue(progressString.contains("total=10"));
        assertTrue(progressString.contains("processed=7"));
    }

    @Test
    void testTaskEstimatedTime() {
        // Given
        Map<String, BufferedImage> images = createTestImages(2);
        BatchOcrProcessor.BatchProcessingOptions options = BatchOcrProcessor.BatchProcessingOptions.createDefault();
        
        when(ocrService.recognizeText(any(BufferedImage.class)))
            .thenReturn(OcrResult.success("Test text", 85));

        // When
        BatchProcessingTask task = batchProcessor.processImages(images, options);
        
        // Initially, estimated time should be -1 (cannot estimate)
        assertEquals(-1, task.getEstimatedRemainingTimeMs());
        
        // Wait for some processing
        waitForTaskCompletion(task, 5000);

        // After completion, remaining time should be 0 or -1 (if task completed too quickly)
        long remainingTime = task.getEstimatedRemainingTimeMs();
        assertTrue(remainingTime == 0 || remainingTime == -1,
                  "Remaining time should be 0 or -1, but was: " + remainingTime);
        assertTrue(task.getProcessingTimeMs() > 0);
    }

    // Helper methods

    private Map<String, BufferedImage> createTestImages(int count) {
        Map<String, BufferedImage> images = new HashMap<>();
        for (int i = 0; i < count; i++) {
            BufferedImage image = createTestImage("Test " + i, 100, 50);
            images.put("test_image_" + i + ".png", image);
        }
        return images;
    }

    private BufferedImage createTestImage(String text, int width, int height) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // White background
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width, height);
        
        // Black text
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("Arial", Font.PLAIN, 12));
        
        FontMetrics fm = g2d.getFontMetrics();
        int x = (width - fm.stringWidth(text)) / 2;
        int y = (height + fm.getHeight()) / 2;
        
        g2d.drawString(text, x, y);
        g2d.dispose();
        
        return image;
    }

    private void waitForTaskCompletion(BatchProcessingTask task, long timeoutMs) {
        long startTime = System.currentTimeMillis();
        while (!task.isCompleted() && (System.currentTimeMillis() - startTime) < timeoutMs) {
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
}
