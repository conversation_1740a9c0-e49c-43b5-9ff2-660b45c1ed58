package com.talkweb.ai.indexer.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 大文件处理器测试类
 */
class LargeFileProcessorTest {

    private LargeFileProcessor processor;

    @BeforeEach
    void setUp() {
        processor = new LargeFileProcessor();
    }

    @Test
    void testIsLargeImage() {
        // Small image
        BufferedImage smallImage = new BufferedImage(800, 600, BufferedImage.TYPE_INT_RGB);
        assertFalse(processor.isLargeImage(smallImage));

        // Large image
        BufferedImage largeImage = new BufferedImage(5000, 4000, BufferedImage.TYPE_INT_RGB);
        assertTrue(processor.isLargeImage(largeImage));
    }

    @Test
    void testProcessSmallImage() {
        // Given
        BufferedImage smallImage = createTestImage(800, 600, "Small Image");
        LargeFileProcessor.LargeFileProcessingOptions options = 
            LargeFileProcessor.LargeFileProcessingOptions.createDefault();

        // When
        LargeFileProcessor.LargeFileProcessingResult result = 
            processor.processLargeImage(smallImage, "small.png", options);

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getChunks());
        assertEquals(1, result.getChunks().size());
        
        LargeFileProcessor.ImageChunk chunk = result.getChunks().get(0);
        assertEquals(smallImage, chunk.getImage());
        assertEquals("small.png", chunk.getOriginalName());
    }

    @Test
    void testProcessLargeImageWithHorizontalSplit() {
        // Given
        BufferedImage largeImage = createTestImage(6000, 4000, "Large Image");
        LargeFileProcessor.LargeFileProcessingOptions options = 
            LargeFileProcessor.LargeFileProcessingOptions.createDefault();
        options.setProcessingStrategy(LargeFileProcessor.LargeFileProcessingOptions.ProcessingStrategy.SPLIT_HORIZONTAL);
        options.setMaxChunkSize(2000);

        // When
        LargeFileProcessor.LargeFileProcessingResult result = 
            processor.processLargeImage(largeImage, "large.png", options);

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getChunks());
        assertTrue(result.getChunks().size() > 1);
        
        // Verify chunks
        for (LargeFileProcessor.ImageChunk chunk : result.getChunks()) {
            assertEquals("large.png", chunk.getOriginalName());
            assertEquals(6000, chunk.getWidth()); // Width should remain the same
            assertTrue(chunk.getHeight() <= 2000); // Height should be limited
            assertNotNull(chunk.getChunkName());
        }
    }

    @Test
    void testProcessLargeImageWithVerticalSplit() {
        // Given
        BufferedImage largeImage = createTestImage(6000, 3000, "Large Image");
        LargeFileProcessor.LargeFileProcessingOptions options = 
            LargeFileProcessor.LargeFileProcessingOptions.createDefault();
        options.setProcessingStrategy(LargeFileProcessor.LargeFileProcessingOptions.ProcessingStrategy.SPLIT_VERTICAL);
        options.setMaxChunkSize(2000);

        // When
        LargeFileProcessor.LargeFileProcessingResult result = 
            processor.processLargeImage(largeImage, "large.png", options);

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getChunks());
        assertTrue(result.getChunks().size() > 1);
        
        // Verify chunks
        for (LargeFileProcessor.ImageChunk chunk : result.getChunks()) {
            assertEquals("large.png", chunk.getOriginalName());
            assertEquals(3000, chunk.getHeight()); // Height should remain the same
            assertTrue(chunk.getWidth() <= 2000); // Width should be limited
        }
    }

    @Test
    void testProcessLargeImageWithGridSplit() {
        // Given
        BufferedImage largeImage = createTestImage(4000, 4000, "Square Image");
        LargeFileProcessor.LargeFileProcessingOptions options = 
            LargeFileProcessor.LargeFileProcessingOptions.createDefault();
        options.setProcessingStrategy(LargeFileProcessor.LargeFileProcessingOptions.ProcessingStrategy.SPLIT_GRID);
        options.setMaxChunkSize(1500);

        // When
        LargeFileProcessor.LargeFileProcessingResult result = 
            processor.processLargeImage(largeImage, "square.png", options);

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getChunks());
        assertTrue(result.getChunks().size() >= 4); // Should be split into at least 4 chunks
        
        // Verify chunks
        for (LargeFileProcessor.ImageChunk chunk : result.getChunks()) {
            assertEquals("square.png", chunk.getOriginalName());
            assertTrue(chunk.getWidth() <= 1500);
            assertTrue(chunk.getHeight() <= 1500);
            assertTrue(chunk.getX() >= 0);
            assertTrue(chunk.getY() >= 0);
        }
    }

    @Test
    void testProcessLargeImageWithAdaptiveSplit() {
        // Test wide image (should use vertical split)
        BufferedImage wideImage = createTestImage(8000, 2000, "Wide Image");
        LargeFileProcessor.LargeFileProcessingOptions options = 
            LargeFileProcessor.LargeFileProcessingOptions.createDefault();
        options.setProcessingStrategy(LargeFileProcessor.LargeFileProcessingOptions.ProcessingStrategy.ADAPTIVE);

        LargeFileProcessor.LargeFileProcessingResult result = 
            processor.processLargeImage(wideImage, "wide.png", options);

        assertTrue(result.isSuccess());
        assertTrue(result.getChunks().size() > 1);
        
        // For wide image, chunks should have same height but different widths
        int firstChunkHeight = result.getChunks().get(0).getHeight();
        for (LargeFileProcessor.ImageChunk chunk : result.getChunks()) {
            assertEquals(firstChunkHeight, chunk.getHeight());
        }
    }

    @Test
    void testProcessLargeImageWithResizeAndSplit() {
        // Given
        BufferedImage largeImage = createTestImage(8000, 6000, "Huge Image");
        LargeFileProcessor.LargeFileProcessingOptions options = 
            LargeFileProcessor.LargeFileProcessingOptions.createDefault();
        options.setProcessingStrategy(LargeFileProcessor.LargeFileProcessingOptions.ProcessingStrategy.RESIZE_AND_SPLIT);

        // When
        LargeFileProcessor.LargeFileProcessingResult result = 
            processor.processLargeImage(largeImage, "huge.png", options);

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getChunks());
        
        // Verify that chunks are smaller than original
        for (LargeFileProcessor.ImageChunk chunk : result.getChunks()) {
            assertTrue(chunk.getWidth() < largeImage.getWidth());
            assertTrue(chunk.getHeight() < largeImage.getHeight());
        }
    }

    @Test
    void testImageChunk() {
        // Given
        BufferedImage testImage = createTestImage(100, 100, "Test");
        LargeFileProcessor.ImageChunk chunk = new LargeFileProcessor.ImageChunk(
            testImage, "original.png", 10, 20, 100, 100);

        // Then
        assertEquals(testImage, chunk.getImage());
        assertEquals("original.png", chunk.getOriginalName());
        assertEquals(10, chunk.getX());
        assertEquals(20, chunk.getY());
        assertEquals(100, chunk.getWidth());
        assertEquals(100, chunk.getHeight());
        
        String chunkName = chunk.getChunkName();
        assertNotNull(chunkName);
        assertTrue(chunkName.contains("original.png"));
        assertTrue(chunkName.contains("10"));
        assertTrue(chunkName.contains("20"));
    }

    @Test
    void testProcessingOptions() {
        // Test default options
        LargeFileProcessor.LargeFileProcessingOptions options = 
            LargeFileProcessor.LargeFileProcessingOptions.createDefault();
        
        assertEquals(LargeFileProcessor.LargeFileProcessingOptions.ProcessingStrategy.ADAPTIVE, 
                    options.getProcessingStrategy());
        assertEquals(2048, options.getMaxChunkSize());
        assertEquals(2, options.getMinChunks());
        assertTrue(options.isPreserveAspectRatio());

        // Test option setters
        options.setProcessingStrategy(LargeFileProcessor.LargeFileProcessingOptions.ProcessingStrategy.SPLIT_GRID);
        options.setMaxChunkSize(1024);
        options.setMinChunks(4);
        options.setPreserveAspectRatio(false);

        assertEquals(LargeFileProcessor.LargeFileProcessingOptions.ProcessingStrategy.SPLIT_GRID, 
                    options.getProcessingStrategy());
        assertEquals(1024, options.getMaxChunkSize());
        assertEquals(4, options.getMinChunks());
        assertFalse(options.isPreserveAspectRatio());

        // Test minimum values
        options.setMaxChunkSize(100); // Should be clamped to 512
        options.setMinChunks(0); // Should be clamped to 1

        assertEquals(512, options.getMaxChunkSize());
        assertEquals(1, options.getMinChunks());
    }

    @Test
    void testProcessingResult() {
        // Test success result
        BufferedImage testImage = createTestImage(100, 100, "Test");
        LargeFileProcessor.ImageChunk chunk = new LargeFileProcessor.ImageChunk(
            testImage, "test.png", 0, 0, 100, 100);
        
        LargeFileProcessor.LargeFileProcessingResult successResult = 
            LargeFileProcessor.LargeFileProcessingResult.success(List.of(chunk));
        
        assertTrue(successResult.isSuccess());
        assertNotNull(successResult.getChunks());
        assertEquals(1, successResult.getChunks().size());
        assertNull(successResult.getErrorMessage());

        // Test failure result
        LargeFileProcessor.LargeFileProcessingResult failureResult = 
            LargeFileProcessor.LargeFileProcessingResult.failure("Processing failed");
        
        assertFalse(failureResult.isSuccess());
        assertNull(failureResult.getChunks());
        assertEquals("Processing failed", failureResult.getErrorMessage());
    }

    @Test
    void testTallImageAdaptiveSplit() {
        // Test tall image (should use horizontal split)
        BufferedImage tallImage = createTestImage(2000, 8000, "Tall Image");
        LargeFileProcessor.LargeFileProcessingOptions options = 
            LargeFileProcessor.LargeFileProcessingOptions.createDefault();
        options.setProcessingStrategy(LargeFileProcessor.LargeFileProcessingOptions.ProcessingStrategy.ADAPTIVE);

        LargeFileProcessor.LargeFileProcessingResult result = 
            processor.processLargeImage(tallImage, "tall.png", options);

        assertTrue(result.isSuccess());
        assertTrue(result.getChunks().size() > 1);
        
        // For tall image, chunks should have same width but different heights
        int firstChunkWidth = result.getChunks().get(0).getWidth();
        for (LargeFileProcessor.ImageChunk chunk : result.getChunks()) {
            assertEquals(firstChunkWidth, chunk.getWidth());
        }
    }

    // Helper methods

    private BufferedImage createTestImage(int width, int height, String text) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // White background
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width, height);
        
        // Black text
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("Arial", Font.PLAIN, Math.max(12, Math.min(width, height) / 20)));
        
        FontMetrics fm = g2d.getFontMetrics();
        int x = (width - fm.stringWidth(text)) / 2;
        int y = (height + fm.getHeight()) / 2;
        
        g2d.drawString(text, x, y);
        g2d.dispose();
        
        return image;
    }
}
