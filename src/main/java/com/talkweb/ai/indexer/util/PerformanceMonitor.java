package com.talkweb.ai.indexer.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.Map;

/**
 * 性能监控工具类
 * 
 * 提供性能指标收集、统计和报告功能，用于监控OCR服务的性能表现。
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class PerformanceMonitor {
    
    private static final Logger logger = LoggerFactory.getLogger(PerformanceMonitor.class);
    
    // 性能指标存储
    private final Map<String, PerformanceMetric> metrics = new ConcurrentHashMap<>();
    
    /**
     * 开始性能测量
     * 
     * @param operationName 操作名称
     * @return 测量上下文
     */
    public MeasurementContext startMeasurement(String operationName) {
        return new MeasurementContext(operationName, System.nanoTime());
    }
    
    /**
     * 记录操作完成
     * 
     * @param context 测量上下文
     */
    public void recordCompletion(MeasurementContext context) {
        long duration = System.nanoTime() - context.startTime;
        recordMetric(context.operationName, duration / 1_000_000); // 转换为毫秒
    }
    
    /**
     * 记录性能指标
     * 
     * @param operationName 操作名称
     * @param durationMs 持续时间（毫秒）
     */
    public void recordMetric(String operationName, long durationMs) {
        metrics.computeIfAbsent(operationName, k -> new PerformanceMetric())
               .record(durationMs);
    }
    
    /**
     * 获取性能指标
     * 
     * @param operationName 操作名称
     * @return 性能指标，如果不存在则返回null
     */
    public PerformanceMetric getMetric(String operationName) {
        return metrics.get(operationName);
    }
    
    /**
     * 获取所有性能指标
     * 
     * @return 所有性能指标的映射
     */
    public Map<String, PerformanceMetric> getAllMetrics() {
        return new ConcurrentHashMap<>(metrics);
    }
    
    /**
     * 清除所有性能指标
     */
    public void clearMetrics() {
        metrics.clear();
        logger.info("Performance metrics cleared");
    }
    
    /**
     * 生成性能报告
     * 
     * @return 性能报告字符串
     */
    public String generateReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== Performance Report ===\n");
        
        if (metrics.isEmpty()) {
            report.append("No performance data available.\n");
            return report.toString();
        }
        
        for (Map.Entry<String, PerformanceMetric> entry : metrics.entrySet()) {
            String operationName = entry.getKey();
            PerformanceMetric metric = entry.getValue();
            
            report.append(String.format("Operation: %s\n", operationName));
            report.append(String.format("  Count: %d\n", metric.getCount()));
            report.append(String.format("  Total Time: %d ms\n", metric.getTotalTime()));
            report.append(String.format("  Average Time: %.2f ms\n", metric.getAverageTime()));
            report.append(String.format("  Min Time: %d ms\n", metric.getMinTime()));
            report.append(String.format("  Max Time: %d ms\n", metric.getMaxTime()));
            report.append("\n");
        }
        
        return report.toString();
    }
    
    /**
     * 记录性能报告到日志
     */
    public void logReport() {
        String report = generateReport();
        logger.info("\n{}", report);
    }
    
    /**
     * 测量上下文类
     */
    public static class MeasurementContext {
        private final String operationName;
        private final long startTime;
        
        private MeasurementContext(String operationName, long startTime) {
            this.operationName = operationName;
            this.startTime = startTime;
        }
        
        public String getOperationName() {
            return operationName;
        }
        
        public long getStartTime() {
            return startTime;
        }
        
        /**
         * 获取已经过的时间（毫秒）
         */
        public long getElapsedTimeMs() {
            return (System.nanoTime() - startTime) / 1_000_000;
        }
    }
    
    /**
     * 性能指标类
     */
    public static class PerformanceMetric {
        private final LongAdder count = new LongAdder();
        private final LongAdder totalTime = new LongAdder();
        private final AtomicLong minTime = new AtomicLong(Long.MAX_VALUE);
        private final AtomicLong maxTime = new AtomicLong(Long.MIN_VALUE);
        
        /**
         * 记录一次测量
         * 
         * @param durationMs 持续时间（毫秒）
         */
        public void record(long durationMs) {
            count.increment();
            totalTime.add(durationMs);
            
            // 更新最小值
            minTime.updateAndGet(current -> Math.min(current, durationMs));
            
            // 更新最大值
            maxTime.updateAndGet(current -> Math.max(current, durationMs));
        }
        
        /**
         * 获取测量次数
         */
        public long getCount() {
            return count.sum();
        }
        
        /**
         * 获取总时间
         */
        public long getTotalTime() {
            return totalTime.sum();
        }
        
        /**
         * 获取平均时间
         */
        public double getAverageTime() {
            long countValue = count.sum();
            return countValue > 0 ? (double) totalTime.sum() / countValue : 0.0;
        }
        
        /**
         * 获取最小时间
         */
        public long getMinTime() {
            long value = minTime.get();
            return value == Long.MAX_VALUE ? 0 : value;
        }
        
        /**
         * 获取最大时间
         */
        public long getMaxTime() {
            long value = maxTime.get();
            return value == Long.MIN_VALUE ? 0 : value;
        }
        
        /**
         * 获取吞吐量（每秒操作数）
         */
        public double getThroughput() {
            long totalTimeMs = totalTime.sum();
            long countValue = count.sum();
            if (totalTimeMs > 0 && countValue > 0) {
                return (double) countValue * 1000 / totalTimeMs;
            }
            return 0.0;
        }
        
        /**
         * 重置指标
         */
        public void reset() {
            count.reset();
            totalTime.reset();
            minTime.set(Long.MAX_VALUE);
            maxTime.set(Long.MIN_VALUE);
        }
        
        @Override
        public String toString() {
            return String.format("PerformanceMetric{count=%d, totalTime=%d ms, avgTime=%.2f ms, " +
                               "minTime=%d ms, maxTime=%d ms, throughput=%.2f ops/sec}",
                               getCount(), getTotalTime(), getAverageTime(), 
                               getMinTime(), getMaxTime(), getThroughput());
        }
    }
    
    /**
     * 性能基准类
     * 用于定义和检查性能基准
     */
    public static class PerformanceBenchmark {
        private final String operationName;
        private final long maxAverageTimeMs;
        private final long maxMaxTimeMs;
        private final double minThroughput;
        
        public PerformanceBenchmark(String operationName, long maxAverageTimeMs, 
                                  long maxMaxTimeMs, double minThroughput) {
            this.operationName = operationName;
            this.maxAverageTimeMs = maxAverageTimeMs;
            this.maxMaxTimeMs = maxMaxTimeMs;
            this.minThroughput = minThroughput;
        }
        
        /**
         * 检查性能指标是否满足基准
         * 
         * @param metric 性能指标
         * @return 基准检查结果
         */
        public BenchmarkResult check(PerformanceMetric metric) {
            if (metric == null) {
                return new BenchmarkResult(false, "No metric data available for " + operationName);
            }
            
            boolean passed = true;
            StringBuilder issues = new StringBuilder();
            
            // 检查平均时间
            if (metric.getAverageTime() > maxAverageTimeMs) {
                passed = false;
                issues.append(String.format("Average time %.2f ms exceeds limit %d ms. ", 
                                           metric.getAverageTime(), maxAverageTimeMs));
            }
            
            // 检查最大时间
            if (metric.getMaxTime() > maxMaxTimeMs) {
                passed = false;
                issues.append(String.format("Max time %d ms exceeds limit %d ms. ", 
                                           metric.getMaxTime(), maxMaxTimeMs));
            }
            
            // 检查吞吐量
            if (metric.getThroughput() < minThroughput) {
                passed = false;
                issues.append(String.format("Throughput %.2f ops/sec below minimum %.2f ops/sec. ", 
                                           metric.getThroughput(), minThroughput));
            }
            
            String message = passed ? "All benchmarks passed" : issues.toString().trim();
            return new BenchmarkResult(passed, message);
        }
        
        // Getters
        public String getOperationName() { return operationName; }
        public long getMaxAverageTimeMs() { return maxAverageTimeMs; }
        public long getMaxMaxTimeMs() { return maxMaxTimeMs; }
        public double getMinThroughput() { return minThroughput; }
    }
    
    /**
     * 基准检查结果类
     */
    public static class BenchmarkResult {
        private final boolean passed;
        private final String message;
        
        public BenchmarkResult(boolean passed, String message) {
            this.passed = passed;
            this.message = message;
        }
        
        public boolean isPassed() { return passed; }
        public String getMessage() { return message; }
        
        @Override
        public String toString() {
            return String.format("BenchmarkResult{passed=%s, message='%s'}", passed, message);
        }
    }
}
