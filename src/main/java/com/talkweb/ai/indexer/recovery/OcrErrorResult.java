package com.talkweb.ai.indexer.recovery;

import com.talkweb.ai.indexer.model.OcrResult;

/**
 * OCR错误处理结果
 * 
 * 封装错误处理的结果和恢复策略
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class OcrErrorResult {
    
    private final OcrErrorHandler.RecoveryStrategy strategy;
    private final String message;
    private final String advice;
    private final long retryDelayMs;
    private final OcrResult fallbackResult;
    private final boolean shouldContinue;
    
    private OcrErrorResult(OcrErrorHandler.RecoveryStrategy strategy, String message, String advice, 
                          long retryDelayMs, OcrResult fallbackResult, boolean shouldContinue) {
        this.strategy = strategy;
        this.message = message;
        this.advice = advice;
        this.retryDelayMs = retryDelayMs;
        this.fallbackResult = fallbackResult;
        this.shouldContinue = shouldContinue;
    }
    
    /**
     * 创建重试结果
     */
    public static OcrErrorResult retry(long delayMs, String advice) {
        return new OcrErrorResult(
            OcrErrorHandler.RecoveryStrategy.RETRY,
            "将在 " + delayMs + "ms 后重试",
            advice,
            delayMs,
            null,
            true
        );
    }
    
    /**
     * 创建降级结果
     */
    public static OcrErrorResult fallback(OcrResult fallbackResult, String advice) {
        return new OcrErrorResult(
            OcrErrorHandler.RecoveryStrategy.FALLBACK,
            "使用降级处理结果",
            advice,
            0,
            fallbackResult,
            true
        );
    }
    
    /**
     * 创建跳过结果
     */
    public static OcrErrorResult skip(String advice) {
        return new OcrErrorResult(
            OcrErrorHandler.RecoveryStrategy.SKIP,
            "跳过当前处理",
            advice,
            0,
            null,
            true
        );
    }
    
    /**
     * 创建失败结果
     */
    public static OcrErrorResult fail(String message, String advice) {
        return new OcrErrorResult(
            OcrErrorHandler.RecoveryStrategy.FAIL,
            message,
            advice,
            0,
            null,
            false
        );
    }
    
    // Getters
    
    public OcrErrorHandler.RecoveryStrategy getStrategy() {
        return strategy;
    }
    
    public String getMessage() {
        return message;
    }
    
    public String getAdvice() {
        return advice;
    }
    
    public long getRetryDelayMs() {
        return retryDelayMs;
    }
    
    public OcrResult getFallbackResult() {
        return fallbackResult;
    }
    
    public boolean shouldContinue() {
        return shouldContinue;
    }
    
    public boolean isRetry() {
        return strategy == OcrErrorHandler.RecoveryStrategy.RETRY;
    }
    
    public boolean isFallback() {
        return strategy == OcrErrorHandler.RecoveryStrategy.FALLBACK;
    }
    
    public boolean isSkip() {
        return strategy == OcrErrorHandler.RecoveryStrategy.SKIP;
    }
    
    public boolean isFail() {
        return strategy == OcrErrorHandler.RecoveryStrategy.FAIL;
    }
    
    @Override
    public String toString() {
        return "OcrErrorResult{" +
                "strategy=" + strategy +
                ", message='" + message + '\'' +
                ", advice='" + advice + '\'' +
                ", retryDelayMs=" + retryDelayMs +
                ", shouldContinue=" + shouldContinue +
                '}';
    }
}
