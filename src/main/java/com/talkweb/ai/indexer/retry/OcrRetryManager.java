package com.talkweb.ai.indexer.retry;

import com.talkweb.ai.indexer.logging.OcrLogger;
import com.talkweb.ai.indexer.metrics.OcrMetrics;
import com.talkweb.ai.indexer.model.OcrResult;
import com.talkweb.ai.indexer.recovery.OcrErrorHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Supplier;

/**
 * OCR重试管理器
 * 
 * 提供智能的重试机制，包括：
 * - 指数退避策略
 * - 重试条件判断
 * - 重试统计和监控
 * - 自适应重试策略
 * - 熔断器模式
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class OcrRetryManager {
    
    private static final Logger logger = LoggerFactory.getLogger(OcrRetryManager.class);
    
    @Autowired
    private OcrErrorHandler errorHandler;
    
    @Autowired
    private OcrLogger ocrLogger;
    
    @Autowired
    private OcrMetrics ocrMetrics;
    
    // 重试配置
    private static final int DEFAULT_MAX_ATTEMPTS = 3;
    private static final long DEFAULT_BASE_DELAY_MS = 1000;
    private static final long DEFAULT_MAX_DELAY_MS = 30000;
    private static final double DEFAULT_BACKOFF_MULTIPLIER = 2.0;
    private static final double DEFAULT_JITTER_FACTOR = 0.1;
    
    // 熔断器配置
    private static final int CIRCUIT_BREAKER_FAILURE_THRESHOLD = 10;
    private static final long CIRCUIT_BREAKER_TIMEOUT_MS = 60000; // 1分钟
    
    // 重试统计
    private final Map<String, RetryStatistics> retryStats = new ConcurrentHashMap<>();
    
    // 熔断器状态
    private volatile CircuitBreakerState circuitBreakerState = CircuitBreakerState.CLOSED;
    private volatile LocalDateTime lastFailureTime = null;
    private volatile int consecutiveFailures = 0;
    
    /**
     * 熔断器状态枚举
     */
    public enum CircuitBreakerState {
        CLOSED,    // 正常状态
        OPEN,      // 熔断状态
        HALF_OPEN  // 半开状态
    }
    
    /**
     * 重试配置类
     */
    public static class RetryConfig {
        private int maxAttempts = DEFAULT_MAX_ATTEMPTS;
        private long baseDelayMs = DEFAULT_BASE_DELAY_MS;
        private long maxDelayMs = DEFAULT_MAX_DELAY_MS;
        private double backoffMultiplier = DEFAULT_BACKOFF_MULTIPLIER;
        private double jitterFactor = DEFAULT_JITTER_FACTOR;
        private boolean enableCircuitBreaker = true;
        
        // Getters and setters
        public int getMaxAttempts() { return maxAttempts; }
        public void setMaxAttempts(int maxAttempts) { this.maxAttempts = maxAttempts; }
        
        public long getBaseDelayMs() { return baseDelayMs; }
        public void setBaseDelayMs(long baseDelayMs) { this.baseDelayMs = baseDelayMs; }
        
        public long getMaxDelayMs() { return maxDelayMs; }
        public void setMaxDelayMs(long maxDelayMs) { this.maxDelayMs = maxDelayMs; }
        
        public double getBackoffMultiplier() { return backoffMultiplier; }
        public void setBackoffMultiplier(double backoffMultiplier) { this.backoffMultiplier = backoffMultiplier; }
        
        public double getJitterFactor() { return jitterFactor; }
        public void setJitterFactor(double jitterFactor) { this.jitterFactor = jitterFactor; }
        
        public boolean isEnableCircuitBreaker() { return enableCircuitBreaker; }
        public void setEnableCircuitBreaker(boolean enableCircuitBreaker) { this.enableCircuitBreaker = enableCircuitBreaker; }
    }
    
    /**
     * 重试统计类
     */
    public static class RetryStatistics {
        private int totalAttempts = 0;
        private int successfulRetries = 0;
        private int failedRetries = 0;
        private long totalRetryTime = 0;
        private LocalDateTime lastRetryTime;
        
        public void recordAttempt() { totalAttempts++; }
        public void recordSuccess(long retryTime) { 
            successfulRetries++; 
            totalRetryTime += retryTime;
            lastRetryTime = LocalDateTime.now();
        }
        public void recordFailure() { 
            failedRetries++; 
            lastRetryTime = LocalDateTime.now();
        }
        
        // Getters
        public int getTotalAttempts() { return totalAttempts; }
        public int getSuccessfulRetries() { return successfulRetries; }
        public int getFailedRetries() { return failedRetries; }
        public long getTotalRetryTime() { return totalRetryTime; }
        public LocalDateTime getLastRetryTime() { return lastRetryTime; }
        public double getSuccessRate() { 
            return totalAttempts > 0 ? (double) successfulRetries / totalAttempts : 0.0; 
        }
        public double getAvgRetryTime() { 
            return successfulRetries > 0 ? (double) totalRetryTime / successfulRetries : 0.0; 
        }
    }
    
    /**
     * 执行带重试的操作
     */
    public <T> T executeWithRetry(String operationName, Supplier<T> operation, RetryConfig config) {
        String requestId = "retry_" + operationName + "_" + System.currentTimeMillis();
        RetryStatistics stats = retryStats.computeIfAbsent(operationName, k -> new RetryStatistics());
        
        // 检查熔断器状态
        if (config.isEnableCircuitBreaker() && !isCircuitBreakerAllowRequest()) {
            throw new RuntimeException("Circuit breaker is OPEN - operation not allowed");
        }
        
        Exception lastException = null;
        long startTime = System.currentTimeMillis();
        
        for (int attempt = 1; attempt <= config.getMaxAttempts(); attempt++) {
            stats.recordAttempt();
            
            try {
                ocrLogger.logProcessingStage(requestId, "retry_attempt_" + attempt, 
                    String.format("执行第 %d 次尝试 (最大 %d 次)", attempt, config.getMaxAttempts()));
                
                T result = operation.get();
                
                // 成功执行
                long retryTime = System.currentTimeMillis() - startTime;
                stats.recordSuccess(retryTime);
                recordCircuitBreakerSuccess();
                
                if (attempt > 1) {
                    logger.info("Operation '{}' succeeded on attempt {}/{}", operationName, attempt, config.getMaxAttempts());
                    ocrLogger.logProcessingStage(requestId, "retry_success", 
                        String.format("重试成功，第 %d 次尝试", attempt));
                }
                
                return result;
                
            } catch (Exception e) {
                lastException = e;
                stats.recordFailure();
                recordCircuitBreakerFailure();
                
                logger.warn("Operation '{}' failed on attempt {}/{}: {}", 
                           operationName, attempt, config.getMaxAttempts(), e.getMessage());
                
                // 记录错误
                Map<String, Object> errorContext = Map.of(
                    "operation", operationName,
                    "attempt", attempt,
                    "maxAttempts", config.getMaxAttempts()
                );
                ocrLogger.logError(requestId, "retry_failure", e.getMessage(), e, errorContext);
                
                // 如果不是最后一次尝试，计算延迟并等待
                if (attempt < config.getMaxAttempts()) {
                    long delay = calculateDelay(attempt, config);
                    
                    ocrLogger.logProcessingStage(requestId, "retry_delay", 
                        String.format("等待 %d ms 后进行第 %d 次重试", delay, attempt + 1));
                    
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Retry interrupted", ie);
                    }
                }
            }
        }
        
        // 所有重试都失败了
        logger.error("Operation '{}' failed after {} attempts", operationName, config.getMaxAttempts());
        throw new RuntimeException("Operation failed after " + config.getMaxAttempts() + " attempts", lastException);
    }
    
    /**
     * 执行带重试的OCR操作
     */
    public OcrResult executeOcrWithRetry(String operationName, Supplier<OcrResult> ocrOperation) {
        return executeOcrWithRetry(operationName, ocrOperation, new RetryConfig());
    }
    
    /**
     * 执行带重试的OCR操作（自定义配置）
     */
    public OcrResult executeOcrWithRetry(String operationName, Supplier<OcrResult> ocrOperation, RetryConfig config) {
        return executeWithRetry(operationName, ocrOperation, config);
    }
    
    /**
     * 计算重试延迟（指数退避 + 抖动）
     */
    private long calculateDelay(int attempt, RetryConfig config) {
        // 指数退避
        long delay = (long) (config.getBaseDelayMs() * Math.pow(config.getBackoffMultiplier(), attempt - 1));
        
        // 限制最大延迟
        delay = Math.min(delay, config.getMaxDelayMs());
        
        // 添加抖动以避免雷群效应
        if (config.getJitterFactor() > 0) {
            double jitter = ThreadLocalRandom.current().nextDouble(-config.getJitterFactor(), config.getJitterFactor());
            delay = (long) (delay * (1 + jitter));
        }
        
        return Math.max(delay, 0);
    }
    
    /**
     * 检查熔断器是否允许请求
     */
    private boolean isCircuitBreakerAllowRequest() {
        switch (circuitBreakerState) {
            case CLOSED:
                return true;
            case OPEN:
                // 检查是否可以转换到半开状态
                if (lastFailureTime != null) {
                    long timeSinceLastFailure = java.time.Duration.between(lastFailureTime, LocalDateTime.now()).toMillis();
                    if (timeSinceLastFailure >= CIRCUIT_BREAKER_TIMEOUT_MS) {
                        circuitBreakerState = CircuitBreakerState.HALF_OPEN;
                        logger.info("Circuit breaker state changed to HALF_OPEN");
                        return true;
                    }
                }
                return false;
            case HALF_OPEN:
                return true;
            default:
                return false;
        }
    }
    
    /**
     * 记录熔断器成功
     */
    private void recordCircuitBreakerSuccess() {
        if (circuitBreakerState == CircuitBreakerState.HALF_OPEN) {
            circuitBreakerState = CircuitBreakerState.CLOSED;
            consecutiveFailures = 0;
            logger.info("Circuit breaker state changed to CLOSED");
        }
    }
    
    /**
     * 记录熔断器失败
     */
    private void recordCircuitBreakerFailure() {
        consecutiveFailures++;
        lastFailureTime = LocalDateTime.now();
        
        if (circuitBreakerState == CircuitBreakerState.CLOSED && 
            consecutiveFailures >= CIRCUIT_BREAKER_FAILURE_THRESHOLD) {
            circuitBreakerState = CircuitBreakerState.OPEN;
            logger.warn("Circuit breaker state changed to OPEN after {} consecutive failures", consecutiveFailures);
        } else if (circuitBreakerState == CircuitBreakerState.HALF_OPEN) {
            circuitBreakerState = CircuitBreakerState.OPEN;
            logger.warn("Circuit breaker state changed back to OPEN");
        }
    }
    
    /**
     * 获取重试统计信息
     */
    public Map<String, RetryStatistics> getRetryStatistics() {
        return new ConcurrentHashMap<>(retryStats);
    }
    
    /**
     * 重置重试统计
     */
    public void resetRetryStatistics() {
        retryStats.clear();
        logger.info("Retry statistics reset");
    }
    
    /**
     * 获取熔断器状态
     */
    public CircuitBreakerState getCircuitBreakerState() {
        return circuitBreakerState;
    }
    
    /**
     * 手动重置熔断器
     */
    public void resetCircuitBreaker() {
        circuitBreakerState = CircuitBreakerState.CLOSED;
        consecutiveFailures = 0;
        lastFailureTime = null;
        logger.info("Circuit breaker manually reset to CLOSED");
    }
    
    /**
     * 获取重试统计报告
     */
    public String getRetryStatisticsReport() {
        StringBuilder report = new StringBuilder();
        report.append("OCR重试统计报告:\n");
        report.append("==================\n");
        report.append(String.format("熔断器状态: %s\n", circuitBreakerState));
        report.append(String.format("连续失败次数: %d\n", consecutiveFailures));
        
        if (retryStats.isEmpty()) {
            report.append("暂无重试记录\n");
        } else {
            retryStats.forEach((operation, stats) -> {
                report.append(String.format("\n操作: %s\n", operation));
                report.append(String.format("  总尝试次数: %d\n", stats.getTotalAttempts()));
                report.append(String.format("  成功重试: %d\n", stats.getSuccessfulRetries()));
                report.append(String.format("  失败重试: %d\n", stats.getFailedRetries()));
                report.append(String.format("  成功率: %.2f%%\n", stats.getSuccessRate() * 100));
                report.append(String.format("  平均重试时间: %.2f ms\n", stats.getAvgRetryTime()));
                if (stats.getLastRetryTime() != null) {
                    report.append(String.format("  最后重试时间: %s\n", stats.getLastRetryTime()));
                }
            });
        }
        
        return report.toString();
    }
}
