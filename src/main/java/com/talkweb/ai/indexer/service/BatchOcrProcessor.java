package com.talkweb.ai.indexer.service;

import com.talkweb.ai.indexer.config.OcrConfiguration;
import com.talkweb.ai.indexer.model.OcrResult;
import com.talkweb.ai.indexer.util.PerformanceMonitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.annotation.PreDestroy;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

/**
 * 批量OCR处理服务
 * 
 * 提供高效的批量图像OCR处理功能，包括：
 * - 批量处理管道
 * - 进度跟踪
 * - 错误恢复
 * - 大文件处理优化
 * - 结果聚合
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Service
public class BatchOcrProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(BatchOcrProcessor.class);
    
    private final OcrService ocrService;
    private final OcrConfiguration ocrConfig;
    private final PerformanceMonitor performanceMonitor;
    
    // 批处理执行器
    private ExecutorService batchExecutor;
    
    // 当前活跃的批处理任务
    private final Map<String, BatchProcessingTask> activeTasks = new ConcurrentHashMap<>();
    
    public BatchOcrProcessor(OcrService ocrService, 
                           OcrConfiguration ocrConfig,
                           PerformanceMonitor performanceMonitor) {
        this.ocrService = ocrService;
        this.ocrConfig = ocrConfig;
        this.performanceMonitor = performanceMonitor;
        
        // 初始化批处理执行器
        int batchThreads = Math.max(2, Runtime.getRuntime().availableProcessors() / 2);
        this.batchExecutor = Executors.newFixedThreadPool(batchThreads, r -> {
            Thread t = new Thread(r, "BatchOCR-" + System.currentTimeMillis());
            t.setDaemon(true);
            return t;
        });
        
        logger.info("BatchOcrProcessor initialized with {} threads", batchThreads);
    }
    
    @PreDestroy
    public void shutdown() {
        if (batchExecutor != null) {
            batchExecutor.shutdown();
            try {
                if (!batchExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    batchExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                batchExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        logger.info("BatchOcrProcessor shutdown completed");
    }
    
    /**
     * 批量处理图像文件
     * 
     * @param imageFiles 图像文件列表
     * @param options 批处理选项
     * @return 批处理任务
     */
    public BatchProcessingTask processFiles(List<File> imageFiles, BatchProcessingOptions options) {
        String taskId = generateTaskId();
        logger.info("Starting batch processing task {} with {} files", taskId, imageFiles.size());
        
        BatchProcessingTask task = new BatchProcessingTask(taskId, imageFiles.size(), options);
        activeTasks.put(taskId, task);
        
        // 异步执行批处理
        CompletableFuture.runAsync(() -> executeBatchProcessing(task, imageFiles, options), batchExecutor)
            .whenComplete((result, throwable) -> {
                if (throwable != null) {
                    logger.error("Batch processing task {} failed", taskId, throwable);
                    task.markFailed(throwable.getMessage());
                } else {
                    logger.info("Batch processing task {} completed successfully", taskId);
                    task.markCompleted();
                }
                
                // 清理完成的任务（延迟清理以便查询结果）
                CompletableFuture.delayedExecutor(5, TimeUnit.MINUTES)
                    .execute(() -> activeTasks.remove(taskId));
            });
        
        return task;
    }
    
    /**
     * 批量处理BufferedImage对象
     * 
     * @param images 图像对象映射（名称 -> 图像）
     * @param options 批处理选项
     * @return 批处理任务
     */
    public BatchProcessingTask processImages(Map<String, BufferedImage> images, BatchProcessingOptions options) {
        String taskId = generateTaskId();
        logger.info("Starting batch processing task {} with {} images", taskId, images.size());
        
        BatchProcessingTask task = new BatchProcessingTask(taskId, images.size(), options);
        activeTasks.put(taskId, task);
        
        // 异步执行批处理
        CompletableFuture.runAsync(() -> executeBatchProcessing(task, images, options), batchExecutor)
            .whenComplete((result, throwable) -> {
                if (throwable != null) {
                    logger.error("Batch processing task {} failed", taskId, throwable);
                    task.markFailed(throwable.getMessage());
                } else {
                    logger.info("Batch processing task {} completed successfully", taskId);
                    task.markCompleted();
                }
                
                // 清理完成的任务
                CompletableFuture.delayedExecutor(5, TimeUnit.MINUTES)
                    .execute(() -> activeTasks.remove(taskId));
            });
        
        return task;
    }
    
    /**
     * 获取批处理任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态，如果任务不存在则返回null
     */
    public BatchProcessingTask getTask(String taskId) {
        return activeTasks.get(taskId);
    }
    
    /**
     * 获取所有活跃的批处理任务
     * 
     * @return 活跃任务列表
     */
    public List<BatchProcessingTask> getActiveTasks() {
        return new ArrayList<>(activeTasks.values());
    }
    
    /**
     * 取消批处理任务
     * 
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    public boolean cancelTask(String taskId) {
        BatchProcessingTask task = activeTasks.get(taskId);
        if (task != null && !task.isCompleted()) {
            task.cancel();
            logger.info("Batch processing task {} cancelled", taskId);
            return true;
        }
        return false;
    }
    
    // 私有方法
    
    private void executeBatchProcessing(BatchProcessingTask task, List<File> imageFiles, BatchProcessingOptions options) {
        PerformanceMonitor.MeasurementContext perfContext = 
            performanceMonitor.startMeasurement("batch_processing_files");
        
        try {
            task.markStarted();
            
            // 分批处理
            int batchSize = options.getBatchSize();
            List<List<File>> batches = partitionList(imageFiles, batchSize);
            
            for (int i = 0; i < batches.size() && !task.isCancelled(); i++) {
                List<File> batch = batches.get(i);
                logger.debug("Processing batch {}/{} with {} files", i + 1, batches.size(), batch.size());
                
                processBatch(task, batch, options);
                
                // 批次间暂停（如果配置了）
                if (options.getBatchDelayMs() > 0) {
                    try {
                        Thread.sleep(options.getBatchDelayMs());
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
            
        } finally {
            performanceMonitor.recordCompletion(perfContext);
        }
    }
    
    private void executeBatchProcessing(BatchProcessingTask task, Map<String, BufferedImage> images, BatchProcessingOptions options) {
        PerformanceMonitor.MeasurementContext perfContext = 
            performanceMonitor.startMeasurement("batch_processing_images");
        
        try {
            task.markStarted();
            
            // 转换为列表并分批处理
            List<Map.Entry<String, BufferedImage>> imageList = new ArrayList<>(images.entrySet());
            int batchSize = options.getBatchSize();
            List<List<Map.Entry<String, BufferedImage>>> batches = partitionList(imageList, batchSize);
            
            for (int i = 0; i < batches.size() && !task.isCancelled(); i++) {
                List<Map.Entry<String, BufferedImage>> batch = batches.get(i);
                logger.debug("Processing batch {}/{} with {} images", i + 1, batches.size(), batch.size());
                
                processBatchImages(task, batch, options);
                
                // 批次间暂停
                if (options.getBatchDelayMs() > 0) {
                    try {
                        Thread.sleep(options.getBatchDelayMs());
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
            
        } finally {
            performanceMonitor.recordCompletion(perfContext);
        }
    }
    
    private void processBatch(BatchProcessingTask task, List<File> batch, BatchProcessingOptions options) {
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        for (File file : batch) {
            if (task.isCancelled()) break;
            
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    OcrResult result = ocrService.recognizeText(file);
                    task.addResult(file.getName(), result);
                    
                    // 调用进度回调
                    if (options.getProgressCallback() != null) {
                        options.getProgressCallback().accept(task.getProgress());
                    }
                    
                } catch (Exception e) {
                    logger.error("Failed to process file: {}", file.getName(), e);
                    task.addError(file.getName(), e.getMessage());
                    
                    // 如果启用了快速失败，取消任务
                    if (options.isFailFast()) {
                        task.cancel();
                    }
                }
            }, batchExecutor);
            
            futures.add(future);
        }
        
        // 等待当前批次完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .get(options.getTimeoutMinutes(), TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("Batch processing failed", e);
            futures.forEach(f -> f.cancel(true));
        }
    }
    
    private void processBatchImages(BatchProcessingTask task, List<Map.Entry<String, BufferedImage>> batch, BatchProcessingOptions options) {
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        for (Map.Entry<String, BufferedImage> entry : batch) {
            if (task.isCancelled()) break;
            
            String imageName = entry.getKey();
            BufferedImage image = entry.getValue();
            
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    OcrResult result = ocrService.recognizeText(image);
                    task.addResult(imageName, result);
                    
                    // 调用进度回调
                    if (options.getProgressCallback() != null) {
                        options.getProgressCallback().accept(task.getProgress());
                    }
                    
                } catch (Exception e) {
                    logger.error("Failed to process image: {}", imageName, e);
                    task.addError(imageName, e.getMessage());
                    
                    // 如果启用了快速失败，取消任务
                    if (options.isFailFast()) {
                        task.cancel();
                    }
                }
            }, batchExecutor);
            
            futures.add(future);
        }
        
        // 等待当前批次完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .get(options.getTimeoutMinutes(), TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("Batch processing failed", e);
            futures.forEach(f -> f.cancel(true));
        }
    }
    
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            partitions.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return partitions;
    }
    
    private String generateTaskId() {
        return "batch_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 批处理选项配置类
     */
    public static class BatchProcessingOptions {
        private int batchSize = 10;                    // 每批处理的文件数
        private long batchDelayMs = 100;               // 批次间延迟（毫秒）
        private int timeoutMinutes = 30;               // 超时时间（分钟）
        private boolean failFast = false;              // 是否快速失败
        private Consumer<BatchProgress> progressCallback; // 进度回调
        private boolean enableRetry = true;            // 是否启用重试
        private int maxRetries = 3;                    // 最大重试次数

        public static BatchProcessingOptions createDefault() {
            return new BatchProcessingOptions();
        }

        public static BatchProcessingOptions createFastProcessing() {
            BatchProcessingOptions options = new BatchProcessingOptions();
            options.setBatchSize(20);
            options.setBatchDelayMs(50);
            options.setTimeoutMinutes(15);
            return options;
        }

        public static BatchProcessingOptions createRobustProcessing() {
            BatchProcessingOptions options = new BatchProcessingOptions();
            options.setBatchSize(5);
            options.setBatchDelayMs(200);
            options.setTimeoutMinutes(60);
            options.setEnableRetry(true);
            options.setMaxRetries(5);
            return options;
        }

        // Getters and Setters
        public int getBatchSize() { return batchSize; }
        public void setBatchSize(int batchSize) { this.batchSize = Math.max(1, batchSize); }

        public long getBatchDelayMs() { return batchDelayMs; }
        public void setBatchDelayMs(long batchDelayMs) { this.batchDelayMs = Math.max(0, batchDelayMs); }

        public int getTimeoutMinutes() { return timeoutMinutes; }
        public void setTimeoutMinutes(int timeoutMinutes) { this.timeoutMinutes = Math.max(1, timeoutMinutes); }

        public boolean isFailFast() { return failFast; }
        public void setFailFast(boolean failFast) { this.failFast = failFast; }

        public Consumer<BatchProgress> getProgressCallback() { return progressCallback; }
        public void setProgressCallback(Consumer<BatchProgress> progressCallback) { this.progressCallback = progressCallback; }

        public boolean isEnableRetry() { return enableRetry; }
        public void setEnableRetry(boolean enableRetry) { this.enableRetry = enableRetry; }

        public int getMaxRetries() { return maxRetries; }
        public void setMaxRetries(int maxRetries) { this.maxRetries = Math.max(0, maxRetries); }
    }

    /**
     * 批处理进度信息
     */
    public static class BatchProgress {
        private final int totalItems;
        private final int processedItems;
        private final int successfulItems;
        private final int failedItems;
        private final double progressPercentage;

        public BatchProgress(int totalItems, int processedItems, int successfulItems, int failedItems) {
            this.totalItems = totalItems;
            this.processedItems = processedItems;
            this.successfulItems = successfulItems;
            this.failedItems = failedItems;
            this.progressPercentage = totalItems > 0 ? (double) processedItems / totalItems * 100.0 : 0.0;
        }

        // Getters
        public int getTotalItems() { return totalItems; }
        public int getProcessedItems() { return processedItems; }
        public int getSuccessfulItems() { return successfulItems; }
        public int getFailedItems() { return failedItems; }
        public double getProgressPercentage() { return progressPercentage; }

        @Override
        public String toString() {
            return String.format("BatchProgress{total=%d, processed=%d, successful=%d, failed=%d, progress=%.1f%%}",
                               totalItems, processedItems, successfulItems, failedItems, progressPercentage);
        }
    }
}
