package com.talkweb.ai.indexer.service;

import com.talkweb.ai.indexer.model.OcrResult;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.Map;

/**
 * 批处理任务类
 * 
 * 跟踪批处理任务的状态、进度和结果。
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class BatchProcessingTask {
    
    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PENDING,    // 等待中
        RUNNING,    // 运行中
        COMPLETED,  // 已完成
        FAILED,     // 失败
        CANCELLED   // 已取消
    }
    
    private final String taskId;
    private final int totalItems;
    private final BatchOcrProcessor.BatchProcessingOptions options;
    private final LocalDateTime createdAt;
    
    private volatile TaskStatus status = TaskStatus.PENDING;
    private volatile LocalDateTime startedAt;
    private volatile LocalDateTime completedAt;
    private volatile String errorMessage;
    
    private final AtomicInteger processedCount = new AtomicInteger(0);
    private final AtomicInteger successCount = new AtomicInteger(0);
    private final AtomicInteger failureCount = new AtomicInteger(0);
    private final AtomicBoolean cancelled = new AtomicBoolean(false);
    
    // 存储处理结果
    private final Map<String, OcrResult> results = new ConcurrentHashMap<>();
    private final Map<String, String> errors = new ConcurrentHashMap<>();
    
    public BatchProcessingTask(String taskId, int totalItems, BatchOcrProcessor.BatchProcessingOptions options) {
        this.taskId = taskId;
        this.totalItems = totalItems;
        this.options = options;
        this.createdAt = LocalDateTime.now();
    }
    
    /**
     * 标记任务开始
     */
    public void markStarted() {
        this.status = TaskStatus.RUNNING;
        this.startedAt = LocalDateTime.now();
    }
    
    /**
     * 标记任务完成
     */
    public void markCompleted() {
        this.status = TaskStatus.COMPLETED;
        this.completedAt = LocalDateTime.now();
    }
    
    /**
     * 标记任务失败
     */
    public void markFailed(String errorMessage) {
        this.status = TaskStatus.FAILED;
        this.errorMessage = errorMessage;
        this.completedAt = LocalDateTime.now();
    }
    
    /**
     * 取消任务
     */
    public void cancel() {
        this.cancelled.set(true);
        this.status = TaskStatus.CANCELLED;
        this.completedAt = LocalDateTime.now();
    }
    
    /**
     * 添加处理结果
     */
    public void addResult(String itemName, OcrResult result) {
        results.put(itemName, result);
        processedCount.incrementAndGet();
        
        if (result.isSuccess()) {
            successCount.incrementAndGet();
        } else {
            failureCount.incrementAndGet();
        }
    }
    
    /**
     * 添加错误信息
     */
    public void addError(String itemName, String errorMessage) {
        errors.put(itemName, errorMessage);
        processedCount.incrementAndGet();
        failureCount.incrementAndGet();
    }
    
    /**
     * 获取当前进度
     */
    public BatchOcrProcessor.BatchProgress getProgress() {
        return new BatchOcrProcessor.BatchProgress(
            totalItems,
            processedCount.get(),
            successCount.get(),
            failureCount.get()
        );
    }
    
    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        int processed = processedCount.get();
        return processed > 0 ? (double) successCount.get() / processed : 0.0;
    }
    
    /**
     * 获取处理时间（毫秒）
     */
    public long getProcessingTimeMs() {
        if (startedAt == null) return 0;
        
        LocalDateTime endTime = completedAt != null ? completedAt : LocalDateTime.now();
        return java.time.Duration.between(startedAt, endTime).toMillis();
    }
    
    /**
     * 获取估计剩余时间（毫秒）
     */
    public long getEstimatedRemainingTimeMs() {
        if (status != TaskStatus.RUNNING || processedCount.get() == 0) {
            return -1; // 无法估计
        }
        
        long processingTime = getProcessingTimeMs();
        int processed = processedCount.get();
        int remaining = totalItems - processed;
        
        if (remaining <= 0) return 0;
        
        double avgTimePerItem = (double) processingTime / processed;
        return (long) (avgTimePerItem * remaining);
    }
    
    /**
     * 获取任务摘要
     */
    public TaskSummary getSummary() {
        return new TaskSummary(
            taskId,
            status,
            totalItems,
            processedCount.get(),
            successCount.get(),
            failureCount.get(),
            getSuccessRate(),
            getProcessingTimeMs(),
            getEstimatedRemainingTimeMs(),
            createdAt,
            startedAt,
            completedAt,
            errorMessage
        );
    }
    
    // Getters
    
    public String getTaskId() { return taskId; }
    public int getTotalItems() { return totalItems; }
    public TaskStatus getStatus() { return status; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public LocalDateTime getStartedAt() { return startedAt; }
    public LocalDateTime getCompletedAt() { return completedAt; }
    public String getErrorMessage() { return errorMessage; }
    public int getProcessedCount() { return processedCount.get(); }
    public int getSuccessCount() { return successCount.get(); }
    public int getFailureCount() { return failureCount.get(); }
    public boolean isCancelled() { return cancelled.get(); }
    public boolean isCompleted() { return status == TaskStatus.COMPLETED || status == TaskStatus.FAILED || status == TaskStatus.CANCELLED; }
    public Map<String, OcrResult> getResults() { return new ConcurrentHashMap<>(results); }
    public Map<String, String> getErrors() { return new ConcurrentHashMap<>(errors); }
    public BatchOcrProcessor.BatchProcessingOptions getOptions() { return options; }
    
    /**
     * 任务摘要类
     */
    public static class TaskSummary {
        private final String taskId;
        private final TaskStatus status;
        private final int totalItems;
        private final int processedItems;
        private final int successfulItems;
        private final int failedItems;
        private final double successRate;
        private final long processingTimeMs;
        private final long estimatedRemainingTimeMs;
        private final LocalDateTime createdAt;
        private final LocalDateTime startedAt;
        private final LocalDateTime completedAt;
        private final String errorMessage;
        
        public TaskSummary(String taskId, TaskStatus status, int totalItems, int processedItems,
                          int successfulItems, int failedItems, double successRate,
                          long processingTimeMs, long estimatedRemainingTimeMs,
                          LocalDateTime createdAt, LocalDateTime startedAt, LocalDateTime completedAt,
                          String errorMessage) {
            this.taskId = taskId;
            this.status = status;
            this.totalItems = totalItems;
            this.processedItems = processedItems;
            this.successfulItems = successfulItems;
            this.failedItems = failedItems;
            this.successRate = successRate;
            this.processingTimeMs = processingTimeMs;
            this.estimatedRemainingTimeMs = estimatedRemainingTimeMs;
            this.createdAt = createdAt;
            this.startedAt = startedAt;
            this.completedAt = completedAt;
            this.errorMessage = errorMessage;
        }
        
        // Getters
        public String getTaskId() { return taskId; }
        public TaskStatus getStatus() { return status; }
        public int getTotalItems() { return totalItems; }
        public int getProcessedItems() { return processedItems; }
        public int getSuccessfulItems() { return successfulItems; }
        public int getFailedItems() { return failedItems; }
        public double getSuccessRate() { return successRate; }
        public long getProcessingTimeMs() { return processingTimeMs; }
        public long getEstimatedRemainingTimeMs() { return estimatedRemainingTimeMs; }
        public LocalDateTime getCreatedAt() { return createdAt; }
        public LocalDateTime getStartedAt() { return startedAt; }
        public LocalDateTime getCompletedAt() { return completedAt; }
        public String getErrorMessage() { return errorMessage; }
        
        @Override
        public String toString() {
            return String.format("TaskSummary{id='%s', status=%s, progress=%d/%d (%.1f%%), success=%.1f%%, time=%dms}",
                               taskId, status, processedItems, totalItems,
                               totalItems > 0 ? (double) processedItems / totalItems * 100 : 0,
                               successRate * 100, processingTimeMs);
        }
    }
}
