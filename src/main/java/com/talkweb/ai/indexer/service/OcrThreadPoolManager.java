package com.talkweb.ai.indexer.service;

import com.talkweb.ai.indexer.config.OcrConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * OCR线程池管理器
 * 
 * 提供高级的线程池管理功能，包括动态调整、监控、负载均衡等特性。
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class OcrThreadPoolManager {
    
    private static final Logger logger = LoggerFactory.getLogger(OcrThreadPoolManager.class);
    
    private final OcrConfiguration ocrConfig;
    
    // 线程池相关
    private ThreadPoolExecutor mainExecutor;
    private ScheduledExecutorService monitoringExecutor;
    private ExecutorService fastTrackExecutor; // 快速通道，用于小图像
    
    // 监控指标
    private final AtomicLong totalTasksSubmitted = new AtomicLong(0);
    private final AtomicLong totalTasksCompleted = new AtomicLong(0);
    private final AtomicLong totalTasksFailed = new AtomicLong(0);
    private final AtomicLong totalProcessingTime = new AtomicLong(0);
    
    // 动态调整参数
    private volatile int currentCorePoolSize;
    private volatile int currentMaxPoolSize;
    private volatile long lastAdjustmentTime = System.currentTimeMillis();
    
    // 性能阈值
    private static final double HIGH_LOAD_THRESHOLD = 0.8;
    private static final double LOW_LOAD_THRESHOLD = 0.3;
    private static final long ADJUSTMENT_INTERVAL_MS = 30000; // 30秒
    
    public OcrThreadPoolManager(OcrConfiguration ocrConfig) {
        this.ocrConfig = ocrConfig;
    }
    
    @PostConstruct
    public void initialize() {
        logger.info("Initializing OCR Thread Pool Manager");
        
        // 计算初始线程池大小
        int corePoolSize = calculateOptimalCorePoolSize();
        int maxPoolSize = calculateOptimalMaxPoolSize();
        
        this.currentCorePoolSize = corePoolSize;
        this.currentMaxPoolSize = maxPoolSize;
        
        // 创建主线程池
        createMainExecutor();
        
        // 创建快速通道线程池
        createFastTrackExecutor();
        
        // 创建监控线程池
        createMonitoringExecutor();
        
        logger.info("OCR Thread Pool Manager initialized successfully");
        logger.info("Core pool size: {}, Max pool size: {}", currentCorePoolSize, currentMaxPoolSize);
    }
    
    @PreDestroy
    public void shutdown() {
        logger.info("Shutting down OCR Thread Pool Manager");
        
        shutdownExecutor(monitoringExecutor, "Monitoring");
        shutdownExecutor(fastTrackExecutor, "FastTrack");
        shutdownExecutor(mainExecutor, "Main");
        
        logger.info("OCR Thread Pool Manager shutdown completed");
    }
    
    /**
     * 提交OCR任务
     */
    public <T> CompletableFuture<T> submitTask(Callable<T> task, boolean isFastTrack) {
        totalTasksSubmitted.incrementAndGet();
        
        ExecutorService executor = isFastTrack ? fastTrackExecutor : mainExecutor;
        
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                T result = task.call();
                totalTasksCompleted.incrementAndGet();
                totalProcessingTime.addAndGet(System.currentTimeMillis() - startTime);
                return result;
            } catch (Exception e) {
                totalTasksFailed.incrementAndGet();
                throw new RuntimeException(e);
            }
        }, executor);
    }
    
    /**
     * 获取线程池统计信息
     */
    public ThreadPoolStats getStats() {
        return new ThreadPoolStats(
            mainExecutor.getCorePoolSize(),
            mainExecutor.getMaximumPoolSize(),
            mainExecutor.getActiveCount(),
            mainExecutor.getPoolSize(),
            mainExecutor.getQueue().size(),
            totalTasksSubmitted.get(),
            totalTasksCompleted.get(),
            totalTasksFailed.get(),
            totalProcessingTime.get()
        );
    }
    
    /**
     * 动态调整线程池大小
     */
    private void adjustThreadPoolSize() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastAdjustmentTime < ADJUSTMENT_INTERVAL_MS) {
            return; // 调整间隔未到
        }
        
        double currentLoad = calculateCurrentLoad();
        logger.debug("Current thread pool load: {:.2f}", currentLoad);
        
        if (currentLoad > HIGH_LOAD_THRESHOLD) {
            // 高负载，增加线程
            increaseThreadPoolSize();
        } else if (currentLoad < LOW_LOAD_THRESHOLD) {
            // 低负载，减少线程
            decreaseThreadPoolSize();
        }
        
        lastAdjustmentTime = currentTime;
    }
    
    private void createMainExecutor() {
        // 使用自定义的ThreadPoolExecutor
        mainExecutor = new ThreadPoolExecutor(
            currentCorePoolSize,
            currentMaxPoolSize,
            60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(100), // 有界队列
            new OcrThreadFactory("OCR-Main"),
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );
        
        // 允许核心线程超时
        mainExecutor.allowCoreThreadTimeOut(true);
    }
    
    private void createFastTrackExecutor() {
        // 快速通道使用较小的线程池
        int fastTrackSize = Math.max(1, currentCorePoolSize / 4);
        fastTrackExecutor = Executors.newFixedThreadPool(
            fastTrackSize, 
            new OcrThreadFactory("OCR-FastTrack")
        );
    }
    
    private void createMonitoringExecutor() {
        monitoringExecutor = Executors.newScheduledThreadPool(
            1, 
            new OcrThreadFactory("OCR-Monitor")
        );
        
        // 定期监控和调整
        monitoringExecutor.scheduleAtFixedRate(
            this::adjustThreadPoolSize, 
            30, 30, TimeUnit.SECONDS
        );
        
        // 定期清理和垃圾回收建议
        monitoringExecutor.scheduleAtFixedRate(
            this::performMaintenanceTasks, 
            5, 5, TimeUnit.MINUTES
        );
    }
    
    private int calculateOptimalCorePoolSize() {
        int configuredSize = ocrConfig.getThreadPoolSize();
        if (configuredSize > 0) {
            return configuredSize;
        }
        
        // 基于CPU核心数和系统负载计算
        int cpuCores = Runtime.getRuntime().availableProcessors();
        return Math.max(2, cpuCores / 2); // OCR是CPU密集型任务
    }
    
    private int calculateOptimalMaxPoolSize() {
        int coreSize = currentCorePoolSize;
        return Math.max(coreSize, coreSize * 2); // 最大为核心大小的2倍
    }
    
    private double calculateCurrentLoad() {
        if (mainExecutor == null) return 0.0;
        
        int activeThreads = mainExecutor.getActiveCount();
        int maxThreads = mainExecutor.getMaximumPoolSize();
        int queueSize = mainExecutor.getQueue().size();
        
        // 综合考虑活跃线程和队列大小
        double threadLoad = (double) activeThreads / maxThreads;
        double queueLoad = Math.min(1.0, queueSize / 50.0); // 队列大小归一化
        
        return Math.max(threadLoad, queueLoad);
    }
    
    private void increaseThreadPoolSize() {
        int newMaxSize = Math.min(currentMaxPoolSize + 2, Runtime.getRuntime().availableProcessors() * 2);
        if (newMaxSize > currentMaxPoolSize) {
            mainExecutor.setMaximumPoolSize(newMaxSize);
            currentMaxPoolSize = newMaxSize;
            logger.info("Increased thread pool max size to: {}", newMaxSize);
        }
    }
    
    private void decreaseThreadPoolSize() {
        int newMaxSize = Math.max(currentMaxPoolSize - 1, currentCorePoolSize);
        if (newMaxSize < currentMaxPoolSize) {
            mainExecutor.setMaximumPoolSize(newMaxSize);
            currentMaxPoolSize = newMaxSize;
            logger.info("Decreased thread pool max size to: {}", newMaxSize);
        }
    }
    
    private void performMaintenanceTasks() {
        // 建议垃圾回收
        if (shouldSuggestGC()) {
            logger.debug("Suggesting garbage collection");
            System.gc();
        }
        
        // 记录内存使用情况
        logMemoryUsage();
    }
    
    private boolean shouldSuggestGC() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        double memoryUsage = (double) (totalMemory - freeMemory) / totalMemory;
        
        return memoryUsage > 0.8; // 内存使用超过80%时建议GC
    }
    
    private void logMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory() / 1024 / 1024; // MB
        long freeMemory = runtime.freeMemory() / 1024 / 1024; // MB
        long usedMemory = totalMemory - freeMemory;
        
        logger.debug("Memory usage: {}MB used, {}MB free, {}MB total", 
                    usedMemory, freeMemory, totalMemory);
    }
    
    private void shutdownExecutor(ExecutorService executor, String name) {
        if (executor != null) {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                    logger.warn("{} executor did not terminate gracefully", name);
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
    
    /**
     * 自定义线程工厂
     */
    private static class OcrThreadFactory implements ThreadFactory {
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;
        
        OcrThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix + "-";
        }
        
        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, namePrefix + threadNumber.getAndIncrement());
            t.setDaemon(false);
            t.setPriority(Thread.NORM_PRIORITY);
            return t;
        }
    }
    
    /**
     * 线程池统计信息
     */
    public static class ThreadPoolStats {
        private final int corePoolSize;
        private final int maxPoolSize;
        private final int activeThreads;
        private final int currentPoolSize;
        private final int queueSize;
        private final long totalTasksSubmitted;
        private final long totalTasksCompleted;
        private final long totalTasksFailed;
        private final long totalProcessingTime;
        
        public ThreadPoolStats(int corePoolSize, int maxPoolSize, int activeThreads, 
                             int currentPoolSize, int queueSize, long totalTasksSubmitted,
                             long totalTasksCompleted, long totalTasksFailed, long totalProcessingTime) {
            this.corePoolSize = corePoolSize;
            this.maxPoolSize = maxPoolSize;
            this.activeThreads = activeThreads;
            this.currentPoolSize = currentPoolSize;
            this.queueSize = queueSize;
            this.totalTasksSubmitted = totalTasksSubmitted;
            this.totalTasksCompleted = totalTasksCompleted;
            this.totalTasksFailed = totalTasksFailed;
            this.totalProcessingTime = totalProcessingTime;
        }
        
        // Getters
        public int getCorePoolSize() { return corePoolSize; }
        public int getMaxPoolSize() { return maxPoolSize; }
        public int getActiveThreads() { return activeThreads; }
        public int getCurrentPoolSize() { return currentPoolSize; }
        public int getQueueSize() { return queueSize; }
        public long getTotalTasksSubmitted() { return totalTasksSubmitted; }
        public long getTotalTasksCompleted() { return totalTasksCompleted; }
        public long getTotalTasksFailed() { return totalTasksFailed; }
        public long getTotalProcessingTime() { return totalProcessingTime; }
        
        public double getSuccessRate() {
            return totalTasksSubmitted > 0 ? (double) totalTasksCompleted / totalTasksSubmitted : 0.0;
        }
        
        public double getAverageProcessingTime() {
            return totalTasksCompleted > 0 ? (double) totalProcessingTime / totalTasksCompleted : 0.0;
        }
        
        @Override
        public String toString() {
            return String.format("ThreadPoolStats{core=%d, max=%d, active=%d, current=%d, queue=%d, " +
                               "submitted=%d, completed=%d, failed=%d, avgTime=%.2fms, successRate=%.2f%%}",
                               corePoolSize, maxPoolSize, activeThreads, currentPoolSize, queueSize,
                               totalTasksSubmitted, totalTasksCompleted, totalTasksFailed,
                               getAverageProcessingTime(), getSuccessRate() * 100);
        }
    }
}
