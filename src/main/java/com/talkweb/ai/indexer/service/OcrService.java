package com.talkweb.ai.indexer.service;

import com.talkweb.ai.indexer.config.OcrConfiguration;
import com.talkweb.ai.indexer.logging.OcrLogger;
import com.talkweb.ai.indexer.metrics.OcrMetrics;
import com.talkweb.ai.indexer.model.OcrResult;
import com.talkweb.ai.indexer.util.PerformanceMonitor;
import io.micrometer.core.instrument.Timer;
import net.sourceforge.tess4j.ITesseract;
import net.sourceforge.tess4j.Tesseract;
import net.sourceforge.tess4j.TesseractException;
import net.sourceforge.tess4j.Word;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.awt.Rectangle;
import java.awt.image.BufferedImage;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * OCR服务类
 *
 * 基于Tesseract OCR引擎的文本识别服务，支持多语言识别、
 * 置信度评估、异步处理和详细的结果分析。
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Service
public class OcrService {

    private static final Logger logger = LoggerFactory.getLogger(OcrService.class);

    private final OcrConfiguration ocrConfig;
    private final OcrThreadPoolManager threadPoolManager;
    private final OcrCacheManager cacheManager;
    private final PerformanceMonitor performanceMonitor;
    private final OcrMetrics ocrMetrics;
    private final OcrLogger ocrLogger;
    private ITesseract tesseract;

    public OcrService(OcrConfiguration ocrConfig,
                     OcrThreadPoolManager threadPoolManager,
                     OcrCacheManager cacheManager,
                     PerformanceMonitor performanceMonitor,
                     OcrMetrics ocrMetrics,
                     OcrLogger ocrLogger) {
        this.ocrConfig = ocrConfig;
        this.threadPoolManager = threadPoolManager;
        this.cacheManager = cacheManager;
        this.performanceMonitor = performanceMonitor;
        this.ocrMetrics = ocrMetrics;
        this.ocrLogger = ocrLogger;
    }

    @PostConstruct
    public void initialize() {
        if (!ocrConfig.isEnabled()) {
            logger.info("OCR service is disabled");
            return;
        }

        try {
            logger.info("Initializing OCR service with configuration: {}", ocrConfig);

            // 初始化Tesseract实例
            tesseract = new Tesseract();

            // 设置数据路径
            if (ocrConfig.getDataPath() != null && !ocrConfig.getDataPath().isEmpty()) {
                tesseract.setDatapath(ocrConfig.getDataPath());
                logger.info("Using custom Tesseract data path: {}", ocrConfig.getDataPath());
            }

            // 设置语言
            tesseract.setLanguage(ocrConfig.getLanguageString());
            logger.info("OCR languages set to: {}", ocrConfig.getLanguageString());

            // 设置页面分割模式
            tesseract.setPageSegMode(ocrConfig.getPageSegmentationMode());

            // 设置OCR引擎模式
            tesseract.setOcrEngineMode(ocrConfig.getOcrEngineMode());

            // 设置自定义变量
            for (Map.Entry<String, String> entry : ocrConfig.getCustomVariables().entrySet()) {
                tesseract.setVariable(entry.getKey(), entry.getValue());
            }

            logger.info("OCR service initialized successfully with optimized parameters");

        } catch (Exception e) {
            logger.error("Failed to initialize OCR service", e);
            throw new RuntimeException("OCR service initialization failed", e);
        }
    }

    @PreDestroy
    public void shutdown() {
        logger.info("OCR service shutdown completed");
    }

    // 性能优化和自适应方法

    /**
     * 根据图像特征选择最佳的页面分割模式
     *
     * @param image 输入图像
     * @return 推荐的PSM模式
     */
    private int selectOptimalPageSegmentationMode(BufferedImage image) {
        if (!ocrConfig.isAdaptivePageSegmentation()) {
            return ocrConfig.getPageSegmentationMode();
        }

        int width = image.getWidth();
        int height = image.getHeight();
        double aspectRatio = (double) width / height;

        // 基于图像特征选择PSM模式
        if (aspectRatio > 5.0) {
            // 宽长条图像，可能是单行文本
            return 7; // SINGLE_LINE
        } else if (aspectRatio < 0.5) {
            // 高长条图像，可能是垂直文本
            return 5; // SINGLE_BLOCK_VERT_TEXT
        } else if (width < 200 || height < 100) {
            // 小图像，可能是单词或字符
            return width < 100 ? 10 : 8; // SINGLE_CHAR : SINGLE_WORD
        } else if (aspectRatio > 2.0) {
            // 宽图像，可能是单行或稀疏文本
            return 11; // SPARSE_TEXT
        } else {
            // 标准文档图像
            return 6; // SINGLE_BLOCK
        }
    }

    /**
     * 优化图像尺寸以提高OCR性能
     *
     * @param image 原始图像
     * @return 优化后的图像
     */
    private BufferedImage optimizeImageSize(BufferedImage image) {
        int maxSize = ocrConfig.getMaxImageSize();
        int width = image.getWidth();
        int height = image.getHeight();

        // 如果图像尺寸在限制范围内，直接返回
        if (width <= maxSize && height <= maxSize) {
            return image;
        }

        // 计算缩放比例
        double scale = Math.min((double) maxSize / width, (double) maxSize / height);
        int newWidth = Math.max(1, (int) (width * scale));  // 确保最小为1
        int newHeight = Math.max(1, (int) (height * scale)); // 确保最小为1

        // 如果缩放后的尺寸太小，设置最小尺寸
        if (newWidth < 50 || newHeight < 50) {
            double minScale = Math.max(50.0 / width, 50.0 / height);
            newWidth = Math.max(50, (int) (width * minScale));
            newHeight = Math.max(50, (int) (height * minScale));
        }

        logger.debug("Resizing image from {}x{} to {}x{} (scale: {:.2f})",
                    width, height, newWidth, newHeight, scale);

        // 创建缩放后的图像
        BufferedImage scaledImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = scaledImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.drawImage(image, 0, 0, newWidth, newHeight, null);
        g2d.dispose();

        return scaledImage;
    }

    /**
     * 生成图像缓存键
     *
     * @param image 图像
     * @return 缓存键
     */
    private String generateCacheKey(BufferedImage image) {
        return cacheManager.generateCacheKey(image, ocrConfig);
    }

    /**
     * 从缓存获取OCR结果
     *
     * @param cacheKey 缓存键
     * @return OCR结果，如果不存在或已过期则返回null
     */
    private OcrResult getCachedResult(String cacheKey) {
        return cacheManager.get(cacheKey);
    }

    /**
     * 将结果存入缓存
     *
     * @param cacheKey 缓存键
     * @param result OCR结果
     */
    private void cacheResult(String cacheKey, OcrResult result) {
        cacheManager.put(cacheKey, result);
    }

    /**
     * 对图像文件执行OCR识别
     *
     * @param imageFile 图像文件
     * @return OCR识别结果
     */
    public OcrResult recognizeText(File imageFile) {
        if (!ocrConfig.isEnabled()) {
            return OcrResult.failure("OCR service is disabled");
        }

        if (imageFile == null || !imageFile.exists()) {
            return OcrResult.failure("Image file does not exist");
        }

        try {
            // 读取图像文件并使用优化的BufferedImage方法
            BufferedImage bufferedImage = javax.imageio.ImageIO.read(imageFile);
            if (bufferedImage == null) {
                return OcrResult.failure("Failed to read image file: " + imageFile.getName());
            }

            logger.debug("Starting optimized OCR recognition for file: {}", imageFile.getName());

            // 使用优化的BufferedImage识别方法
            OcrResult result = recognizeText(bufferedImage);

            // 添加文件信息到结果中
            if (result.getOcrConfig() != null) {
                Map<String, Object> configInfo = new HashMap<>(result.getOcrConfig());
                configInfo.put("sourceFile", imageFile.getName());
                configInfo.put("fileSize", imageFile.length());
                result.setOcrConfig(configInfo);
            }

            logger.debug("Optimized OCR recognition completed for file: {}, confidence: {}, processing time: {}ms",
                        imageFile.getName(), result.getConfidence(), result.getProcessingTimeMs());

            return result;

        } catch (Exception e) {
            logger.error("Unexpected error during OCR recognition for file: {}", imageFile.getName(), e);
            return OcrResult.failure("Unexpected error: " + e.getMessage());
        }
    }

    /**
     * 对BufferedImage执行OCR识别
     *
     * @param image BufferedImage对象
     * @return OCR识别结果
     */
    public OcrResult recognizeText(BufferedImage image) {
        if (!ocrConfig.isEnabled()) {
            return OcrResult.failure("OCR service is disabled");
        }

        if (image == null) {
            return OcrResult.failure("Image is null");
        }

        long startTime = System.currentTimeMillis();

        // 开始日志记录
        String requestId = ocrLogger.startProcessing(image, "buffered_image");

        // 开始性能监控
        String operationName = String.format("ocr_image_%dx%d", image.getWidth(), image.getHeight());
        PerformanceMonitor.MeasurementContext perfContext = performanceMonitor.startMeasurement(operationName);

        // 开始指标收集
        Timer.Sample metricsTimer = ocrMetrics.startProcessing();

        try {
            logger.debug("Starting optimized OCR recognition for BufferedImage");

            // 0. 检查Tesseract是否已初始化
            if (tesseract == null) {
                return OcrResult.failure("OCR service not properly initialized - tesseract is null");
            }

            // 1. 检查缓存
            ocrLogger.logProcessingStage(requestId, "cache_check", "Checking cache for existing result");
            String cacheKey = generateCacheKey(image);
            OcrResult cachedResult = getCachedResult(cacheKey);
            if (cachedResult != null) {
                logger.debug("OCR result found in cache, returning cached result");
                ocrMetrics.recordCacheHit();
                ocrLogger.logCacheOperation(requestId, "get", true, cacheKey);
                ocrLogger.logProcessingComplete(requestId, cachedResult);
                return cachedResult;
            } else {
                ocrMetrics.recordCacheMiss();
                ocrLogger.logCacheOperation(requestId, "get", false, cacheKey);
            }

            // 2. 图像尺寸优化
            ocrLogger.logProcessingStage(requestId, "image_optimization", "Optimizing image size for OCR");
            BufferedImage optimizedImage = optimizeImageSize(image);
            ocrLogger.logImagePreprocessing(requestId, "size_optimization",
                image.getWidth(), image.getHeight(),
                optimizedImage.getWidth(), optimizedImage.getHeight());

            // 3. 自适应页面分割模式
            ocrLogger.logProcessingStage(requestId, "psm_selection", "Selecting optimal page segmentation mode");
            int optimalPsm = selectOptimalPageSegmentationMode(optimizedImage);
            int currentPsm = ocrConfig.getPageSegmentationMode();
            if (optimalPsm != currentPsm) {
                tesseract.setPageSegMode(optimalPsm);
                logger.debug("Adaptive PSM: switched from {} to mode {}", currentPsm, optimalPsm);
                ocrLogger.logProcessingStage(requestId, "psm_switch",
                    String.format("Switched PSM from %d to %d", currentPsm, optimalPsm));
            }

            // 4. 执行OCR识别
            ocrLogger.logProcessingStage(requestId, "ocr_recognition", "Performing OCR text recognition");
            String text = tesseract.doOCR(optimizedImage);

            // 5. 获取详细结果
            ocrLogger.logProcessingStage(requestId, "word_extraction", "Extracting word-level details");
            List<Word> words = tesseract.getWords(optimizedImage, 1);

            // 6. 计算整体置信度
            ocrLogger.logProcessingStage(requestId, "confidence_calculation", "Calculating overall confidence");
            float overallConfidence = calculateOverallConfidence(words);

            // 7. 创建结果对象
            OcrResult result = createOcrResult(text, overallConfidence, words, startTime);

            // 8. 添加配置信息和优化信息
            Map<String, Object> configInfo = createConfigInfo();
            configInfo.put("adaptivePsm", optimalPsm);
            configInfo.put("imageOptimized", optimizedImage != image);
            configInfo.put("originalSize", image.getWidth() + "x" + image.getHeight());
            configInfo.put("processedSize", optimizedImage.getWidth() + "x" + optimizedImage.getHeight());
            result.setOcrConfig(configInfo);

            // 9. 缓存结果
            cacheResult(cacheKey, result);

            // 10. 记录成功指标
            int imageSize = image.getWidth() * image.getHeight();
            ocrMetrics.recordProcessingSuccess(metricsTimer, overallConfidence, imageSize);

            // 11. 记录性能信息
            Map<String, Object> perfMetrics = Map.of(
                "confidence", overallConfidence,
                "text_length", text != null ? text.length() : 0,
                "word_count", words != null ? words.size() : 0,
                "psm_mode", optimalPsm
            );
            ocrLogger.logPerformance(requestId, "ocr_recognition", result.getProcessingTimeMs(), perfMetrics);

            // 12. 完成日志记录
            ocrLogger.logProcessingComplete(requestId, result);

            logger.debug("Optimized OCR recognition completed for BufferedImage, confidence: {}, processing time: {}ms, PSM: {}",
                        overallConfidence, result.getProcessingTimeMs(), optimalPsm);

            return result;

        } catch (TesseractException e) {
            logger.error("OCR recognition failed for BufferedImage", e);
            ocrMetrics.recordProcessingError(metricsTimer, "tesseract_error");

            // 记录详细错误信息
            Map<String, Object> errorContext = Map.of(
                "image_size", image.getWidth() + "x" + image.getHeight(),
                "tesseract_version", "4.x",
                "processing_stage", "ocr_recognition"
            );
            ocrLogger.logError(requestId, "tesseract_error", e.getMessage(), e, errorContext);

            OcrResult failureResult = OcrResult.failure("OCR recognition failed: " + e.getMessage());
            ocrLogger.logProcessingComplete(requestId, failureResult);
            return failureResult;
        } catch (Exception e) {
            logger.error("Unexpected error during OCR recognition for BufferedImage", e);
            ocrMetrics.recordProcessingError(metricsTimer, "unexpected_error");

            // 记录详细错误信息
            Map<String, Object> errorContext = Map.of(
                "image_size", image.getWidth() + "x" + image.getHeight(),
                "error_class", e.getClass().getSimpleName(),
                "processing_stage", "unknown"
            );
            ocrLogger.logError(requestId, "unexpected_error", e.getMessage(), e, errorContext);

            OcrResult failureResult = OcrResult.failure("Unexpected error: " + e.getMessage());
            ocrLogger.logProcessingComplete(requestId, failureResult);
            return failureResult;
        } finally {
            // 记录性能指标
            performanceMonitor.recordCompletion(perfContext);
        }
    }

    /**
     * 异步执行OCR识别
     *
     * @param imageFile 图像文件
     * @return CompletableFuture包装的OCR结果
     */
    public CompletableFuture<OcrResult> recognizeTextAsync(File imageFile) {
        // 先进行基本的null检查
        if (imageFile == null) {
            return CompletableFuture.completedFuture(
                OcrResult.failure("Image file is null"));
        }

        // 判断是否使用快速通道（小文件）
        boolean isFastTrack = imageFile.length() < 1024 * 1024; // 1MB以下使用快速通道

        return threadPoolManager.submitTask(() -> recognizeText(imageFile), isFastTrack)
            .orTimeout(ocrConfig.getTimeoutSeconds(), TimeUnit.SECONDS)
            .exceptionally(throwable -> {
                if (throwable instanceof TimeoutException) {
                    logger.warn("OCR recognition timeout for file: {}", imageFile.getName());
                    return OcrResult.timeout();
                } else {
                    logger.error("Async OCR recognition failed for file: {}", imageFile.getName(), throwable);
                    return OcrResult.failure("Async OCR failed: " + throwable.getMessage());
                }
            });
    }

    /**
     * 异步执行OCR识别
     *
     * @param image BufferedImage对象
     * @return CompletableFuture包装的OCR结果
     */
    public CompletableFuture<OcrResult> recognizeTextAsync(BufferedImage image) {
        // 先进行基本的null检查
        if (image == null) {
            return CompletableFuture.completedFuture(
                OcrResult.failure("Image is null"));
        }

        // 判断是否使用快速通道（小图像）
        boolean isFastTrack = image.getWidth() * image.getHeight() < 500 * 500; // 小于500x500使用快速通道

        return threadPoolManager.submitTask(() -> recognizeText(image), isFastTrack)
            .orTimeout(ocrConfig.getTimeoutSeconds(), TimeUnit.SECONDS)
            .exceptionally(throwable -> {
                if (throwable instanceof TimeoutException) {
                    logger.warn("OCR recognition timeout for BufferedImage");
                    return OcrResult.timeout();
                } else {
                    logger.error("Async OCR recognition failed for BufferedImage", throwable);
                    return OcrResult.failure("Async OCR failed: " + throwable.getMessage());
                }
            });
    }

    /**
     * 检查OCR服务是否可用
     *
     * @return 如果服务可用返回true
     */
    public boolean isAvailable() {
        return ocrConfig.isEnabled() && tesseract != null;
    }

    /**
     * 获取OCR配置信息
     *
     * @return 配置信息映射
     */
    public Map<String, Object> getConfigInfo() {
        return createConfigInfo();
    }

    // Private helper methods

    private float calculateOverallConfidence(List<Word> words) {
        if (words == null || words.isEmpty()) {
            return 0.0f;
        }

        float totalConfidence = 0.0f;
        int validWords = 0;

        for (Word word : words) {
            if (word.getConfidence() > 0) {
                totalConfidence += word.getConfidence();
                validWords++;
            }
        }

        return validWords > 0 ? totalConfidence / validWords : 0.0f;
    }

    private OcrResult createOcrResult(String text, float confidence, List<Word> words, long startTime) {
        long processingTime = System.currentTimeMillis() - startTime;

        // 确定状态 - 使用优化后的阈值逻辑
        OcrResult.Status status;
        if (confidence >= ocrConfig.getHighQualityThreshold()) {
            status = OcrResult.Status.SUCCESS;
        } else if (confidence >= ocrConfig.getConfidenceThreshold()) {
            status = OcrResult.Status.SUCCESS; // 仍然认为是成功，但质量较低
        } else if (confidence > 0) {
            status = OcrResult.Status.LOW_CONFIDENCE;
        } else {
            status = OcrResult.Status.FAILED;
        }

        OcrResult result = new OcrResult(status, text != null ? text.trim() : "", confidence);
        result.setProcessingTimeMs(processingTime);

        // 转换单词结果
        if (words != null && !words.isEmpty()) {
            List<OcrResult.WordResult> wordResults = new ArrayList<>();
            for (Word word : words) {
                Rectangle boundingBox = word.getBoundingBox();
                wordResults.add(new OcrResult.WordResult(
                    word.getText(),
                    word.getConfidence(),
                    boundingBox
                ));
            }
            result.setWords(wordResults);
        }

        return result;
    }

    private Map<String, Object> createConfigInfo() {
        Map<String, Object> config = new HashMap<>();
        config.put("languages", ocrConfig.getLanguages());
        config.put("pageSegmentationMode", ocrConfig.getPageSegmentationMode());
        config.put("ocrEngineMode", ocrConfig.getOcrEngineMode());
        config.put("confidenceThreshold", ocrConfig.getConfidenceThreshold());
        config.put("highQualityThreshold", ocrConfig.getHighQualityThreshold());
        config.put("preprocessingEnabled", ocrConfig.isPreprocessingEnabled());
        config.put("timeoutSeconds", ocrConfig.getTimeoutSeconds());
        config.put("adaptivePageSegmentation", ocrConfig.isAdaptivePageSegmentation());
        config.put("maxImageSize", ocrConfig.getMaxImageSize());
        config.put("imageCacheEnabled", ocrConfig.isImageCacheEnabled());
        config.put("cacheSize", ocrConfig.getCacheSize());
        config.put("threadPoolSize", ocrConfig.getThreadPoolSize());
        return config;
    }

    // 新增的管理和监控方法

    /**
     * 获取线程池统计信息
     */
    public OcrThreadPoolManager.ThreadPoolStats getThreadPoolStats() {
        return threadPoolManager.getStats();
    }

    /**
     * 获取缓存统计信息
     */
    public OcrCacheManager.CacheStats getCacheStats() {
        return cacheManager.getStats();
    }

    /**
     * 清空缓存
     */
    public void clearCache() {
        cacheManager.clear();
        logger.info("OCR cache cleared manually");
    }

    /**
     * 获取性能指标
     */
    public Map<String, PerformanceMonitor.PerformanceMetric> getPerformanceMetrics() {
        return performanceMonitor.getAllMetrics();
    }

    /**
     * 生成性能报告
     */
    public String generatePerformanceReport() {
        return performanceMonitor.generateReport();
    }

    /**
     * 清除性能指标
     */
    public void clearPerformanceMetrics() {
        performanceMonitor.clearMetrics();
    }

    /**
     * 获取服务状态信息
     */
    public Map<String, Object> getServiceStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("enabled", ocrConfig.isEnabled());
        status.put("threadPoolStats", getThreadPoolStats());
        status.put("cacheStats", getCacheStats());
        status.put("performanceMetrics", getPerformanceMetrics());
        status.put("configuration", createConfigInfo());
        return status;
    }
}
