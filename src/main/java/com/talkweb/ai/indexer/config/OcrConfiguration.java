package com.talkweb.ai.indexer.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * OCR引擎配置类
 * 
 * 提供Tesseract OCR引擎的详细配置选项，包括语言设置、
 * 页面分割模式、引擎模式、置信度阈值等参数。
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
@ConfigurationProperties(prefix = "app.ocr")
public class OcrConfiguration {
    
    /**
     * 是否启用OCR功能
     */
    private boolean enabled = true;
    
    /**
     * OCR识别语言列表
     * 支持多语言，如: ["eng", "chi_sim", "chi_tra"]
     */
    private List<String> languages = List.of("eng", "chi_sim");
    
    /**
     * 页面分割模式 (Page Segmentation Mode)
     * 0 = OSD_ONLY: 仅方向和脚本检测
     * 1 = AUTO_OSD: 自动页面分割与OSD
     * 2 = AUTO_ONLY: 自动页面分割，无OSD或OCR
     * 3 = AUTO: 完全自动页面分割，无OSD (默认)
     * 4 = SINGLE_COLUMN: 假设单列可变大小的文本
     * 5 = SINGLE_BLOCK_VERT_TEXT: 假设单个统一的垂直对齐文本块
     * 6 = SINGLE_BLOCK: 假设单个统一的文本块
     * 7 = SINGLE_LINE: 将图像视为单个文本行
     * 8 = SINGLE_WORD: 将图像视为单个单词
     * 9 = CIRCLE_WORD: 将图像视为圆圈中的单个单词
     * 10 = SINGLE_CHAR: 将图像视为单个字符
     * 11 = SPARSE_TEXT: 稀疏文本，按任意顺序查找尽可能多的文本
     * 12 = SPARSE_TEXT_OSD: 稀疏文本与OSD
     * 13 = RAW_LINE: 原始行，将图像视为单个文本行，绕过特定于Tesseract的黑客
     */
    private int pageSegmentationMode = 6; // SINGLE_BLOCK - 优化后的默认值，适合大多数文档图像

    /**
     * 自适应页面分割模式
     * 根据图像特征自动选择最佳的PSM模式
     */
    private boolean adaptivePageSegmentation = true;
    
    /**
     * OCR引擎模式 (OCR Engine Mode)
     * 0 = LEGACY_ONLY: 仅传统引擎
     * 1 = NEURAL_NETS_LSTM_ONLY: 仅神经网络LSTM引擎
     * 2 = LEGACY_PLUS_LSTM: 传统 + LSTM引擎
     * 3 = DEFAULT: 默认，基于可用内容 (推荐)
     */
    private int ocrEngineMode = 1; // NEURAL_NETS_LSTM_ONLY - 优化后使用LSTM引擎，提高准确率

    /**
     * 置信度阈值 (0-100)
     * 低于此阈值的识别结果将被标记为低质量
     * 优化后降低阈值以获得更多识别结果
     */
    private int confidenceThreshold = 50; // 从60降低到50，平衡准确率和召回率

    /**
     * 高质量置信度阈值 (0-100)
     * 高于此阈值的识别结果被认为是高质量的
     */
    private int highQualityThreshold = 80;
    
    /**
     * Tesseract数据文件路径
     * 如果为空，将使用系统默认路径
     */
    private String dataPath;
    
    /**
     * 是否启用图像预处理
     */
    private boolean preprocessingEnabled = true;
    
    /**
     * 超时时间（秒）
     * OCR处理单个图像的最大时间
     */
    private int timeoutSeconds = 30;
    
    /**
     * 是否启用详细日志
     */
    private boolean verboseLogging = false;
    
    /**
     * 自定义Tesseract配置变量
     * 例如: {"tessedit_char_whitelist": "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"}
     */
    private java.util.Map<String, String> customVariables = new java.util.HashMap<>();

    // 性能优化参数

    /**
     * 最大图像尺寸（像素）
     * 超过此尺寸的图像将被缩放以提高处理速度
     */
    private int maxImageSize = 2048;

    /**
     * 是否启用图像缓存
     */
    private boolean imageCacheEnabled = true;

    /**
     * 缓存大小（MB）
     */
    private int cacheSize = 100;

    /**
     * OCR线程池大小
     * 0表示使用CPU核心数
     */
    private int threadPoolSize = 0;
    
    // Constructors
    public OcrConfiguration() {
    }
    
    // Getters and Setters
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public List<String> getLanguages() {
        return languages;
    }
    
    public void setLanguages(List<String> languages) {
        this.languages = languages != null ? languages : List.of("eng");
    }
    
    /**
     * 获取语言字符串，用于Tesseract
     * @return 语言字符串，如 "eng+chi_sim"
     */
    public String getLanguageString() {
        return languages != null && !languages.isEmpty() 
            ? String.join("+", languages) 
            : "eng";
    }
    
    public int getPageSegmentationMode() {
        return pageSegmentationMode;
    }
    
    public void setPageSegmentationMode(int pageSegmentationMode) {
        if (pageSegmentationMode < 0 || pageSegmentationMode > 13) {
            throw new IllegalArgumentException("Page segmentation mode must be between 0 and 13");
        }
        this.pageSegmentationMode = pageSegmentationMode;
    }
    
    public int getOcrEngineMode() {
        return ocrEngineMode;
    }
    
    public void setOcrEngineMode(int ocrEngineMode) {
        if (ocrEngineMode < 0 || ocrEngineMode > 3) {
            throw new IllegalArgumentException("OCR engine mode must be between 0 and 3");
        }
        this.ocrEngineMode = ocrEngineMode;
    }
    
    public int getConfidenceThreshold() {
        return confidenceThreshold;
    }
    
    public void setConfidenceThreshold(int confidenceThreshold) {
        if (confidenceThreshold < 0 || confidenceThreshold > 100) {
            throw new IllegalArgumentException("Confidence threshold must be between 0 and 100");
        }
        this.confidenceThreshold = confidenceThreshold;
    }
    
    public String getDataPath() {
        return dataPath;
    }
    
    public void setDataPath(String dataPath) {
        this.dataPath = dataPath;
    }
    
    public boolean isPreprocessingEnabled() {
        return preprocessingEnabled;
    }
    
    public void setPreprocessingEnabled(boolean preprocessingEnabled) {
        this.preprocessingEnabled = preprocessingEnabled;
    }
    
    public int getTimeoutSeconds() {
        return timeoutSeconds;
    }
    
    public void setTimeoutSeconds(int timeoutSeconds) {
        if (timeoutSeconds <= 0) {
            throw new IllegalArgumentException("Timeout must be positive");
        }
        this.timeoutSeconds = timeoutSeconds;
    }
    
    public boolean isVerboseLogging() {
        return verboseLogging;
    }
    
    public void setVerboseLogging(boolean verboseLogging) {
        this.verboseLogging = verboseLogging;
    }
    
    public java.util.Map<String, String> getCustomVariables() {
        return customVariables;
    }
    
    public void setCustomVariables(java.util.Map<String, String> customVariables) {
        this.customVariables = customVariables != null ? customVariables : new java.util.HashMap<>();
    }

    // 新增字段的getter和setter方法

    public boolean isAdaptivePageSegmentation() {
        return adaptivePageSegmentation;
    }

    public void setAdaptivePageSegmentation(boolean adaptivePageSegmentation) {
        this.adaptivePageSegmentation = adaptivePageSegmentation;
    }

    public int getHighQualityThreshold() {
        return highQualityThreshold;
    }

    public void setHighQualityThreshold(int highQualityThreshold) {
        if (highQualityThreshold < 0 || highQualityThreshold > 100) {
            throw new IllegalArgumentException("High quality threshold must be between 0 and 100");
        }
        this.highQualityThreshold = highQualityThreshold;
    }

    public int getMaxImageSize() {
        return maxImageSize;
    }

    public void setMaxImageSize(int maxImageSize) {
        if (maxImageSize <= 0) {
            throw new IllegalArgumentException("Max image size must be positive");
        }
        this.maxImageSize = maxImageSize;
    }

    public boolean isImageCacheEnabled() {
        return imageCacheEnabled;
    }

    public void setImageCacheEnabled(boolean imageCacheEnabled) {
        this.imageCacheEnabled = imageCacheEnabled;
    }

    public int getCacheSize() {
        return cacheSize;
    }

    public void setCacheSize(int cacheSize) {
        if (cacheSize < 0) {
            throw new IllegalArgumentException("Cache size must be non-negative");
        }
        this.cacheSize = cacheSize;
    }

    public int getThreadPoolSize() {
        return threadPoolSize;
    }

    public void setThreadPoolSize(int threadPoolSize) {
        if (threadPoolSize < 0) {
            throw new IllegalArgumentException("Thread pool size must be non-negative");
        }
        this.threadPoolSize = threadPoolSize;
    }

    /**
     * 验证配置的有效性
     * @return 如果配置有效返回true
     */
    public boolean isValid() {
        return languages != null && !languages.isEmpty() &&
               pageSegmentationMode >= 0 && pageSegmentationMode <= 13 &&
               ocrEngineMode >= 0 && ocrEngineMode <= 3 &&
               confidenceThreshold >= 0 && confidenceThreshold <= 100 &&
               highQualityThreshold >= 0 && highQualityThreshold <= 100 &&
               timeoutSeconds > 0 &&
               maxImageSize > 0 &&
               cacheSize >= 0 &&
               threadPoolSize >= 0;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OcrConfiguration that = (OcrConfiguration) o;
        return enabled == that.enabled &&
               pageSegmentationMode == that.pageSegmentationMode &&
               ocrEngineMode == that.ocrEngineMode &&
               confidenceThreshold == that.confidenceThreshold &&
               preprocessingEnabled == that.preprocessingEnabled &&
               timeoutSeconds == that.timeoutSeconds &&
               verboseLogging == that.verboseLogging &&
               adaptivePageSegmentation == that.adaptivePageSegmentation &&
               highQualityThreshold == that.highQualityThreshold &&
               maxImageSize == that.maxImageSize &&
               imageCacheEnabled == that.imageCacheEnabled &&
               cacheSize == that.cacheSize &&
               threadPoolSize == that.threadPoolSize &&
               Objects.equals(languages, that.languages) &&
               Objects.equals(dataPath, that.dataPath) &&
               Objects.equals(customVariables, that.customVariables);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(enabled, languages, pageSegmentationMode, ocrEngineMode,
                          confidenceThreshold, dataPath, preprocessingEnabled,
                          timeoutSeconds, verboseLogging, customVariables,
                          adaptivePageSegmentation, highQualityThreshold, maxImageSize,
                          imageCacheEnabled, cacheSize, threadPoolSize);
    }
    
    @Override
    public String toString() {
        return "OcrConfiguration{" +
               "enabled=" + enabled +
               ", languages=" + languages +
               ", pageSegmentationMode=" + pageSegmentationMode +
               ", ocrEngineMode=" + ocrEngineMode +
               ", confidenceThreshold=" + confidenceThreshold +
               ", dataPath='" + dataPath + '\'' +
               ", preprocessingEnabled=" + preprocessingEnabled +
               ", timeoutSeconds=" + timeoutSeconds +
               ", verboseLogging=" + verboseLogging +
               ", customVariables=" + customVariables +
               ", adaptivePageSegmentation=" + adaptivePageSegmentation +
               ", highQualityThreshold=" + highQualityThreshold +
               ", maxImageSize=" + maxImageSize +
               ", imageCacheEnabled=" + imageCacheEnabled +
               ", cacheSize=" + cacheSize +
               ", threadPoolSize=" + threadPoolSize +
               '}';
    }
}
