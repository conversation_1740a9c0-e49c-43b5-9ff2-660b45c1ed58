package com.talkweb.ai.indexer.fallback;

import com.talkweb.ai.indexer.logging.OcrLogger;
import com.talkweb.ai.indexer.metrics.OcrMetrics;
import com.talkweb.ai.indexer.model.OcrResult;
import com.talkweb.ai.indexer.recovery.OcrErrorHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.awt.image.BufferedImage;
import java.io.File;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * OCR降级处理管理器
 * 
 * 提供智能的降级处理策略，包括：
 * - 多种降级方案
 * - 降级策略选择
 * - 降级结果质量评估
 * - 降级统计和监控
 * - 自适应降级策略
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class OcrFallbackManager {
    
    private static final Logger logger = LoggerFactory.getLogger(OcrFallbackManager.class);
    
    @Autowired
    private OcrLogger ocrLogger;
    
    @Autowired
    private OcrMetrics ocrMetrics;
    
    // 降级统计
    private final Map<String, FallbackStatistics> fallbackStats = new ConcurrentHashMap<>();
    
    /**
     * 降级策略枚举
     */
    public enum FallbackStrategy {
        SIMPLE_TEXT_EXTRACTION("简单文本提取", "使用基础图像处理提取文本"),
        METADATA_EXTRACTION("元数据提取", "提取文件元数据信息"),
        FILENAME_BASED("文件名推断", "基于文件名推断内容"),
        TEMPLATE_MATCHING("模板匹配", "使用预定义模板匹配"),
        ALTERNATIVE_OCR("备用OCR引擎", "使用备用OCR引擎"),
        MANUAL_REVIEW("人工审核", "标记为需要人工审核"),
        SKIP_PROCESSING("跳过处理", "跳过当前文档处理"),
        CACHED_RESULT("缓存结果", "使用历史缓存结果");
        
        private final String displayName;
        private final String description;
        
        FallbackStrategy(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
    }
    
    /**
     * 降级配置类
     */
    public static class FallbackConfig {
        private List<FallbackStrategy> strategies = new ArrayList<>();
        private boolean enableQualityCheck = true;
        private double minQualityThreshold = 0.1; // 降低默认质量阈值
        private boolean enableFallbackChain = true;
        private int maxFallbackAttempts = 3;
        
        public FallbackConfig() {
            // 默认降级策略链
            strategies.add(FallbackStrategy.ALTERNATIVE_OCR);
            strategies.add(FallbackStrategy.SIMPLE_TEXT_EXTRACTION);
            strategies.add(FallbackStrategy.METADATA_EXTRACTION);
            strategies.add(FallbackStrategy.FILENAME_BASED);
            strategies.add(FallbackStrategy.MANUAL_REVIEW);
        }
        
        // Getters and setters
        public List<FallbackStrategy> getStrategies() { return strategies; }
        public void setStrategies(List<FallbackStrategy> strategies) { this.strategies = strategies; }
        
        public boolean isEnableQualityCheck() { return enableQualityCheck; }
        public void setEnableQualityCheck(boolean enableQualityCheck) { this.enableQualityCheck = enableQualityCheck; }
        
        public double getMinQualityThreshold() { return minQualityThreshold; }
        public void setMinQualityThreshold(double minQualityThreshold) { this.minQualityThreshold = minQualityThreshold; }
        
        public boolean isEnableFallbackChain() { return enableFallbackChain; }
        public void setEnableFallbackChain(boolean enableFallbackChain) { this.enableFallbackChain = enableFallbackChain; }
        
        public int getMaxFallbackAttempts() { return maxFallbackAttempts; }
        public void setMaxFallbackAttempts(int maxFallbackAttempts) { this.maxFallbackAttempts = maxFallbackAttempts; }
    }
    
    /**
     * 降级统计类
     */
    public static class FallbackStatistics {
        private int totalAttempts = 0;
        private int successfulFallbacks = 0;
        private int failedFallbacks = 0;
        private double totalQualityScore = 0.0;
        private LocalDateTime lastFallbackTime;
        private final Map<FallbackStrategy, Integer> strategyUsage = new ConcurrentHashMap<>();
        
        public void recordAttempt(FallbackStrategy strategy) { 
            totalAttempts++; 
            strategyUsage.merge(strategy, 1, Integer::sum);
        }
        
        public void recordSuccess(double qualityScore) { 
            successfulFallbacks++; 
            totalQualityScore += qualityScore;
            lastFallbackTime = LocalDateTime.now();
        }
        
        public void recordFailure() { 
            failedFallbacks++; 
            lastFallbackTime = LocalDateTime.now();
        }
        
        // Getters
        public int getTotalAttempts() { return totalAttempts; }
        public int getSuccessfulFallbacks() { return successfulFallbacks; }
        public int getFailedFallbacks() { return failedFallbacks; }
        public LocalDateTime getLastFallbackTime() { return lastFallbackTime; }
        public Map<FallbackStrategy, Integer> getStrategyUsage() { return new ConcurrentHashMap<>(strategyUsage); }
        
        public double getSuccessRate() { 
            return totalAttempts > 0 ? (double) successfulFallbacks / totalAttempts : 0.0; 
        }
        
        public double getAvgQualityScore() { 
            return successfulFallbacks > 0 ? totalQualityScore / successfulFallbacks : 0.0; 
        }
    }
    
    /**
     * 执行降级处理
     */
    public OcrResult executeFallback(String operationName, OcrErrorHandler.ErrorType errorType, 
                                   BufferedImage image, FallbackConfig config) {
        String requestId = "fallback_" + operationName + "_" + System.currentTimeMillis();
        FallbackStatistics stats = fallbackStats.computeIfAbsent(operationName, k -> new FallbackStatistics());
        
        ocrLogger.logProcessingStage(requestId, "fallback_start", 
            String.format("开始降级处理，错误类型: %s", errorType.getDescription()));
        
        List<FallbackStrategy> strategies = selectStrategies(errorType, config);
        
        for (int attempt = 0; attempt < config.getMaxFallbackAttempts() && attempt < strategies.size(); attempt++) {
            FallbackStrategy strategy = strategies.get(attempt);
            stats.recordAttempt(strategy);
            
            try {
                ocrLogger.logProcessingStage(requestId, "fallback_attempt_" + (attempt + 1), 
                    String.format("尝试降级策略: %s", strategy.getDisplayName()));
                
                OcrResult result = executeFallbackStrategy(strategy, image, requestId);
                
                if (result != null && result.isSuccess()) {
                    // 质量检查
                    double qualityScore = evaluateResultQuality(result, strategy);
                    
                    if (!config.isEnableQualityCheck() || qualityScore >= config.getMinQualityThreshold()) {
                        stats.recordSuccess(qualityScore);
                        
                        logger.info("Fallback strategy '{}' succeeded with quality score: {:.2f}", 
                                   strategy.getDisplayName(), qualityScore);
                        
                        ocrLogger.logProcessingStage(requestId, "fallback_success", 
                            String.format("降级策略成功: %s, 质量分数: %.2f", strategy.getDisplayName(), qualityScore));
                        
                        // 标记结果为降级处理
                        return createFallbackResult(result, strategy, qualityScore);
                    } else {
                        logger.debug("Fallback strategy '{}' result quality too low: {} < {}",
                                    strategy.getDisplayName(), qualityScore, config.getMinQualityThreshold());
                    }
                }
                
            } catch (Exception e) {
                logger.warn("Fallback strategy '{}' failed: {}", strategy.getDisplayName(), e.getMessage());
                ocrLogger.logError(requestId, "fallback_error", e.getMessage(), e, 
                    Map.of("strategy", strategy.getDisplayName(), "attempt", attempt + 1));
            }
            
            if (!config.isEnableFallbackChain()) {
                break; // 不使用降级链，只尝试第一个策略
            }
        }
        
        // 所有降级策略都失败了
        stats.recordFailure();
        logger.error("All fallback strategies failed for operation: {}", operationName);
        
        ocrLogger.logProcessingStage(requestId, "fallback_failed", "所有降级策略都失败了");
        
        return createFailureFallbackResult(errorType);
    }
    
    /**
     * 执行文件降级处理
     */
    public OcrResult executeFallback(String operationName, OcrErrorHandler.ErrorType errorType, 
                                   File file, FallbackConfig config) {
        // 对于文件，我们可以尝试读取文件信息
        try {
            // 这里可以添加文件特定的降级策略
            return executeFallbackForFile(operationName, errorType, file, config);
        } catch (Exception e) {
            logger.error("File fallback processing failed: {}", e.getMessage());
            return createFailureFallbackResult(errorType);
        }
    }
    
    /**
     * 根据错误类型选择降级策略
     */
    private List<FallbackStrategy> selectStrategies(OcrErrorHandler.ErrorType errorType, FallbackConfig config) {
        List<FallbackStrategy> selectedStrategies = new ArrayList<>();
        
        switch (errorType) {
            case TESSERACT_ERROR:
                selectedStrategies.add(FallbackStrategy.ALTERNATIVE_OCR);
                selectedStrategies.add(FallbackStrategy.SIMPLE_TEXT_EXTRACTION);
                selectedStrategies.add(FallbackStrategy.TEMPLATE_MATCHING);
                break;
                
            case TIMEOUT_ERROR:
                selectedStrategies.add(FallbackStrategy.SIMPLE_TEXT_EXTRACTION);
                selectedStrategies.add(FallbackStrategy.METADATA_EXTRACTION);
                selectedStrategies.add(FallbackStrategy.FILENAME_BASED);
                break;
                
            case MEMORY_ERROR:
                selectedStrategies.add(FallbackStrategy.METADATA_EXTRACTION);
                selectedStrategies.add(FallbackStrategy.FILENAME_BASED);
                selectedStrategies.add(FallbackStrategy.SKIP_PROCESSING);
                break;
                
            case IMAGE_FORMAT_ERROR:
                selectedStrategies.add(FallbackStrategy.METADATA_EXTRACTION);
                selectedStrategies.add(FallbackStrategy.FILENAME_BASED);
                selectedStrategies.add(FallbackStrategy.MANUAL_REVIEW);
                break;
                
            case IO_ERROR:
                selectedStrategies.add(FallbackStrategy.CACHED_RESULT);
                selectedStrategies.add(FallbackStrategy.FILENAME_BASED);
                selectedStrategies.add(FallbackStrategy.SKIP_PROCESSING);
                break;
                
            default:
                selectedStrategies.addAll(config.getStrategies());
                break;
        }
        
        // 如果配置中有特定策略，优先使用配置的策略
        if (!config.getStrategies().isEmpty()) {
            List<FallbackStrategy> configStrategies = new ArrayList<>(config.getStrategies());
            configStrategies.retainAll(selectedStrategies); // 取交集
            if (!configStrategies.isEmpty()) {
                selectedStrategies = configStrategies;
            }
        }
        
        return selectedStrategies;
    }
    
    /**
     * 执行具体的降级策略
     */
    private OcrResult executeFallbackStrategy(FallbackStrategy strategy, BufferedImage image, String requestId) {
        switch (strategy) {
            case SIMPLE_TEXT_EXTRACTION:
                return executeSimpleTextExtraction(image, requestId);
                
            case METADATA_EXTRACTION:
                return executeMetadataExtraction(requestId);
                
            case FILENAME_BASED:
                return executeFilenameBased(requestId);
                
            case TEMPLATE_MATCHING:
                return executeTemplateMatching(image, requestId);
                
            case ALTERNATIVE_OCR:
                return executeAlternativeOcr(image, requestId);
                
            case MANUAL_REVIEW:
                return executeManualReview(requestId);
                
            case SKIP_PROCESSING:
                return executeSkipProcessing(requestId);
                
            case CACHED_RESULT:
                return executeCachedResult(requestId);
                
            default:
                throw new UnsupportedOperationException("Unsupported fallback strategy: " + strategy);
        }
    }
    
    // 具体的降级策略实现方法
    
    private OcrResult executeSimpleTextExtraction(BufferedImage image, String requestId) {
        // 简单的文本提取逻辑（示例实现）
        ocrLogger.logProcessingStage(requestId, "simple_extraction", "执行简单文本提取");
        
        // 这里可以实现基础的图像处理和文本提取
        String extractedText = "简单文本提取结果 - 图像尺寸: " + image.getWidth() + "x" + image.getHeight();
        
        return OcrResult.success(extractedText, 0.5f); // 较低的置信度
    }
    
    private OcrResult executeMetadataExtraction(String requestId) {
        // 元数据提取逻辑
        ocrLogger.logProcessingStage(requestId, "metadata_extraction", "执行元数据提取");
        
        String metadataText = "元数据提取结果 - 处理时间: " + LocalDateTime.now();
        return OcrResult.success(metadataText, 0.3f);
    }
    
    private OcrResult executeFilenameBased(String requestId) {
        // 基于文件名的推断
        ocrLogger.logProcessingStage(requestId, "filename_based", "执行文件名推断");
        
        String filenameText = "基于文件名的推断结果";
        return OcrResult.success(filenameText, 0.2f);
    }
    
    private OcrResult executeTemplateMatching(BufferedImage image, String requestId) {
        // 模板匹配逻辑
        ocrLogger.logProcessingStage(requestId, "template_matching", "执行模板匹配");
        
        String templateText = "模板匹配结果";
        return OcrResult.success(templateText, 0.4f);
    }
    
    private OcrResult executeAlternativeOcr(BufferedImage image, String requestId) {
        // 备用OCR引擎逻辑
        ocrLogger.logProcessingStage(requestId, "alternative_ocr", "执行备用OCR引擎");
        
        // 这里可以集成其他OCR引擎
        String alternativeText = "备用OCR引擎结果";
        return OcrResult.success(alternativeText, 0.7f);
    }
    
    private OcrResult executeManualReview(String requestId) {
        // 人工审核标记
        ocrLogger.logProcessingStage(requestId, "manual_review", "标记为人工审核");
        
        String reviewText = "需要人工审核 - 请求ID: " + requestId;
        return OcrResult.success(reviewText, 0.1f);
    }
    
    private OcrResult executeSkipProcessing(String requestId) {
        // 跳过处理
        ocrLogger.logProcessingStage(requestId, "skip_processing", "跳过处理");
        
        return OcrResult.failure("处理被跳过");
    }
    
    private OcrResult executeCachedResult(String requestId) {
        // 缓存结果查找
        ocrLogger.logProcessingStage(requestId, "cached_result", "查找缓存结果");
        
        // 这里可以实现缓存查找逻辑
        String cachedText = "缓存结果";
        return OcrResult.success(cachedText, 0.6f);
    }
    
    private OcrResult executeFallbackForFile(String operationName, OcrErrorHandler.ErrorType errorType, 
                                           File file, FallbackConfig config) {
        // 文件特定的降级处理
        String requestId = "file_fallback_" + operationName + "_" + System.currentTimeMillis();
        
        // 可以提取文件名、大小、修改时间等信息
        String fileInfo = String.format("文件信息 - 名称: %s, 大小: %d bytes, 修改时间: %d", 
                                       file.getName(), file.length(), file.lastModified());
        
        return OcrResult.success(fileInfo, 0.3f);
    }
    
    /**
     * 评估降级结果质量
     */
    private double evaluateResultQuality(OcrResult result, FallbackStrategy strategy) {
        double baseQuality = result.getConfidence() / 100.0; // 转换为0-1范围
        
        // 根据策略调整质量分数
        switch (strategy) {
            case ALTERNATIVE_OCR:
                return baseQuality * 0.9; // 备用OCR质量较高
            case SIMPLE_TEXT_EXTRACTION:
                return baseQuality * 0.7;
            case TEMPLATE_MATCHING:
                return baseQuality * 0.8;
            case METADATA_EXTRACTION:
                return baseQuality * 0.5;
            case FILENAME_BASED:
                return baseQuality * 0.3;
            case CACHED_RESULT:
                return baseQuality * 0.8;
            case MANUAL_REVIEW:
                return 0.1; // 人工审核质量最低，但可以后续处理
            case SKIP_PROCESSING:
                return 0.0;
            default:
                return baseQuality;
        }
    }
    
    /**
     * 创建降级结果
     */
    private OcrResult createFallbackResult(OcrResult originalResult, FallbackStrategy strategy, double qualityScore) {
        // 在原结果基础上添加降级信息
        String fallbackText = originalResult.getText() + 
                             String.format("\n[降级处理: %s, 质量分数: %.2f]", 
                                         strategy.getDisplayName(), qualityScore);
        
        return OcrResult.success(fallbackText, (float) (qualityScore * 100));
    }
    
    /**
     * 创建失败降级结果
     */
    private OcrResult createFailureFallbackResult(OcrErrorHandler.ErrorType errorType) {
        String failureText = String.format("降级处理失败 - 原始错误: %s", errorType.getDescription());
        return OcrResult.failure(failureText);
    }
    
    /**
     * 获取降级统计信息
     */
    public Map<String, FallbackStatistics> getFallbackStatistics() {
        return new ConcurrentHashMap<>(fallbackStats);
    }
    
    /**
     * 重置降级统计
     */
    public void resetFallbackStatistics() {
        fallbackStats.clear();
        logger.info("Fallback statistics reset");
    }
    
    /**
     * 获取降级统计报告
     */
    public String getFallbackStatisticsReport() {
        StringBuilder report = new StringBuilder();
        report.append("OCR降级处理统计报告:\n");
        report.append("========================\n");
        
        if (fallbackStats.isEmpty()) {
            report.append("暂无降级处理记录\n");
        } else {
            fallbackStats.forEach((operation, stats) -> {
                report.append(String.format("\n操作: %s\n", operation));
                report.append(String.format("  总尝试次数: %d\n", stats.getTotalAttempts()));
                report.append(String.format("  成功降级: %d\n", stats.getSuccessfulFallbacks()));
                report.append(String.format("  失败降级: %d\n", stats.getFailedFallbacks()));
                report.append(String.format("  成功率: %.2f%%\n", stats.getSuccessRate() * 100));
                report.append(String.format("  平均质量分数: %.2f\n", stats.getAvgQualityScore()));
                
                if (stats.getLastFallbackTime() != null) {
                    report.append(String.format("  最后降级时间: %s\n", stats.getLastFallbackTime()));
                }
                
                report.append("  策略使用统计:\n");
                stats.getStrategyUsage().forEach((strategy, count) -> {
                    report.append(String.format("    %s: %d 次\n", strategy.getDisplayName(), count));
                });
            });
        }
        
        return report.toString();
    }
}
